# System Patterns: Openfront

## Architecture Overview

-   **Frontend:** Next.js (React framework) for the storefront application (`/storefront`, `/app/(storefront)`).
-   **Backend/CMS:** KeystoneJS (`/keystone`) providing a GraphQL API and admin UI.
-   **Database:** Prisma ORM (`schema.prisma`) likely managing the database schema and interactions.
-   **Styling:** Tailwind CSS (`tailwind.config.js`, `postcss.config.mjs`) and Radix UI components (`/components/ui`).
-   **Language:** TypeScript (`tsconfig.json`).

## Key Technical Decisions & Patterns

-   **Full-Stack TypeScript:** Leveraging TypeScript across both frontend (Next.js) and backend (KeystoneJS) for type safety.
-   **Component-Based UI:** Utilizing React components, likely organized by feature/module (`/storefront/modules`, `/components`).
-   **GraphQL API:** KeystoneJS exposes a GraphQL API for data fetching and mutations.
-   **Type Safety Enhancement Initiative:** An ongoing effort to systematically reduce the usage of `any` types throughout the codebase to improve reliability and maintainability. This involves:
    -   Using ESLint with `@typescript-eslint` rules to identify `any` usage.
    -   Adopting a cautious, incremental approach to replacing `any` with specific types.
    -   Prioritizing accuracy and avoiding breakage over speed.