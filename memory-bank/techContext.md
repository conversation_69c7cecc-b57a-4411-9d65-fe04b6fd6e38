# Tech Context: Openfront

## Core Technologies

-   **Frameworks:** Next.js (v15+), KeystoneJS (v6+)
-   **Language:** TypeScript (v5.5+)
-   **Database:** Unknown (Prisma supports PostgreSQL, MySQL, SQLite, SQL Server, MongoDB)
-   **ORM:** Prisma (v6.5+)
-   **UI Libraries:** <PERSON>act (v19+), Radix UI, Tailwind CSS, clsx, tailwind-merge, lucide-react
-   **State Management:** SWR (likely for data fetching)
-   **Forms:** React Hook Form, Zod (for validation)
-   **API:** GraphQL (via KeystoneJS and Yoga)
-   **Styling:** Tailwind CSS, PostCSS
-   **Package Manager:** npm

## Development Setup & Tooling

-   **Linting:** ESLint configured via `.eslintrc.json`.
    -   Uses `@typescript-eslint/parser` for TypeScript.
    -   Extends `plugin:@typescript-eslint/recommended` and `next/core-web-vitals`.
    -   Requires `eslint-config-next` to be installed as a dev dependency.
    -   Currently configured to *warn* on explicit `any` usage (`@typescript-eslint/no-explicit-any`).
    -   Run via `npm run lint`.
-   **TypeScript Configuration:** Defined in `tsconfig.json`.
-   **Build Process:** Uses `next build` and `keystone build`.
-   **Migrations:** Handled by KeystoneJS using Prisma Migrate (`npm run migrate`, `npm run migrate:gen`).

## Type Verification Resources

-   **GraphQL API Introspection:**
    -   Endpoint: `http://localhost:3000/api/graphql` (Assumed standard Keystone path, adjust if needed)
    -   Method: Send POST requests (e.g., using `curl` or a GraphQL client).
    -   Authentication: Requires `x-api-key` header (value: `cm7pag5lk002s6lq83d747l2j`).
    -   Usage: Introspect specific types, queries, or mutations (e.g., `authenticatedItem`, `Product` type fields) to determine expected shapes and types. Avoid fetching the entire schema (`__schema`) to prevent excessive context usage.
-   **GitHub MCP Tool:**
    -   Server Name: `github` (Assuming this is the server name)
    -   Repository: `keystonejs/keystone`
    -   Usage: Reference KeystoneJS's internal type definitions as a guide, especially for types related to the admin UI or core functionality.
    -   Priority: If Keystone's types conflict with this project's specific implementation or functionality, prioritize creating accurate types for this project.

## Dependencies & Constraints

-   Requires Node.js environment compatible with specified package versions.
-   Relies on external services like payment providers (Stripe mentioned) and shipping providers (EasyPost, ShipEngine, Shippo mentioned).
-   The project uses `overrides` in `package.json` to enforce specific versions of `graphql`, `next`, `react`, and `react-dom`.