# Active Context: Dashboard Authentication Implementation

## Current Focus

-   Implement proper sign-in functionality for the refactored dashboard (`/app/dashboard`).
-   Replace the currently hardcoded `X-API-Key` used for backend communication with a session-based or token-based authentication mechanism, likely leveraging KeystoneJS's authentication system.
-   Ensure the dashboard routes are protected and require authentication.

## Recent Changes (as of 2025-04-07 ~5:48 AM CT)

1.  **Storefront Refactor Completed:** The storefront application (`/app/(storefront)`, `/storefront`) has been successfully refactored to TypeScript/Next.js and is considered functional.
2.  **Dashboard Refactor Partially Completed:** Significant portions of the dashboard UI and functionality have been refactored into the new Next.js structure (`/app/dashboard`, `/components`). However, backend interactions currently rely on a hardcoded API key (`techContext.md` notes key `cm7pag5lk002s6lq83d747l2j`).
3.  **ESLint & Initial Setup:** Tooling for type safety (ESLint) was configured previously.

## Next Steps

1.  **Implement Dashboard Sign-in:**
    -   Analyze the authentication logic in the old codebase (`/Users/<USER>/openfront-old2`).
    -   Implement sign-in UI components (likely reusing or adapting existing ones like `app/dashboard/signin/`).
    -   Integrate with KeystoneJS's authentication mutations/queries via the GraphQL API.
    -   Establish session management or token handling on the client-side.
    -   Remove the hardcoded `X-API-Key` usage.
2.  **Implement Dashboard Route Protection:** Use Next.js middleware or similar patterns to protect dashboard routes, redirecting unauthenticated users to the sign-in page.
3.  **Continue Dashboard Refactor:** Once authentication is working, proceed with refactoring remaining dashboard features.
4.  **Address Old "App" and "Platform" Pages:** Plan the refactoring of features corresponding to the "app pages" and "platform pages" mentioned in the old codebase.

## Decisions & Considerations

-   **Authentication Strategy:** Determine the best approach (sessions vs. JWT) based on KeystoneJS capabilities and the old application's pattern. KeystoneJS typically uses session-based auth.
-   **Leverage Keystone Auth:** Utilize KeystoneJS's built-in authentication features (`useMutation`, `authenticatedItem` query, etc.) as much as possible.
-   **Old Codebase Reference:** The old codebase (`/Users/<USER>/openfront-old2`) serves as a reference for required functionality and logic, but the implementation should follow Next.js/TypeScript best practices.
-   **Hardcoded Key Removal:** The hardcoded API key is a temporary workaround and **must** be removed as part of the authentication implementation.