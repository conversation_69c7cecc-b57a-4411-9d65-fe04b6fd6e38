import { GraphQLClient, RequestDocument, Variables } from "graphql-request";
import { parse, DocumentNode } from "graphql";

const shouldRetry = (error: any): boolean => // Added type for error
  error.message?.includes("connection pool") ||
  error.message?.includes("Timed out") ||
  error.code === "P2024" ||
  error.message?.includes("Code: 502") ||
  error.response?.status === 502 ||
  error.message?.includes("too many clients already") ||
  (error.response?.errors || []).some((e: any) => e.message?.includes("502")); // Added type for e

const isEndpointUnreachable = (error: any): boolean => // Added type for error
  error.code === "ECONNREFUSED" ||
  error.type === "system" ||
  error.message?.includes("request to") ||
  error.message?.includes("failed, reason") ||
  error.message?.includes("DEPLOYMENT_NOT_FOUND") ||
  error.response?.error?.includes("DEPLOYMENT_NOT_FOUND") ||
  error.message?.includes("Code: 404");

const getEmptyResponseForQuery = (query: RequestDocument): Record<string, any[] | null> => {
  const document = typeof query === "string" ? parse(query) : query;
  const emptyResponse: Record<string, any[] | null> = {}; // Added index signature

  document.definitions.forEach((def) => {
    if (def.kind === "OperationDefinition") {
      def.selectionSet.selections.forEach((selection) => {
        if (selection.kind === "Field") {
          const name = selection.name.value;
          const isPlural = name.endsWith("s");
          emptyResponse[name] = isPlural ? [] : null;
        }
      });
    }
  });

  return emptyResponse;
};

// Factory function using composition instead of inheritance
const createRetryingClient = (endpoint: string) => { // Removed options parameter
  const client = new GraphQLClient(endpoint); // Removed options from constructor

  return {
    async request<T = any, V extends Variables = Variables>(
      document: RequestDocument,
      variables?: V,
      requestHeaders?: HeadersInit // Using global HeadersInit or inference
    ): Promise<T> {
      try {
        // Use the internal client instance
        const response = await client.request(document, variables, requestHeaders); // Removed explicit generics <T, V>
        return response as T; // Cast response to T
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.log(`GraphQL error: ${errorMessage}`);
        // Ensure the fallback response shape matches T, casting for now.
        return getEmptyResponseForQuery(document) as T;
      }
    }
  };
};

export const openfrontClient = createRetryingClient(
  `${process.env.FRONTEND_URL}/api/graphql`
  // options can be passed here if needed, e.g., { fetch }
);

