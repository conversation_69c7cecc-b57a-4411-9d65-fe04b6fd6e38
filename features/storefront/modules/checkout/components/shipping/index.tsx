"use client"

import { RadioGroup, Radio } from "@headlessui/react"
import { setShippingMethod } from "@/features/storefront/lib/data/cart" 
import { formatAmount } from "@/features/storefront/lib/util/prices" 
import { CheckCircle, CircleCheck } from "lucide-react" 
import { <PERSON><PERSON> } from "@/components/ui/button" // Shadcn Button
import { cn } from "@/lib/utils/cn"
import ErrorMessage from "@/features/storefront/modules/checkout/components/error-message"
import Divider from "@/features/storefront/modules/common/components/divider"
import MedusaRadio from "@/features/storefront/modules/common/components/radio"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { RiLoader2Fill } from "@remixicon/react";

type ShippingProps = {
  cart: any 
  availableShippingMethods: any[] | null 
}

const Shipping: React.FC<ShippingProps> = ({
  cart,
  availableShippingMethods,
}) => {
  const [isLoading, setIsLoading] = useState(false)
  // Removed isLoadingPrices and calculatedPricesMap states
  const [error, setError] = useState<string | null>(null)
  const [selectedOption, setSelectedOption] = useState<string | null>(
    cart?.shippingMethods?.[0]?.shippingOption?.id || null
  )

  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()

  const isOpen = searchParams?.get("step") === "delivery"

  // Simplified filtering - assuming all methods are shipping (no pickup)
  const _shippingMethods = availableShippingMethods

  // Removed pickup related logic and useEffect for price calculation

  const handleEdit = () => {
    router.push(pathname + "?step=delivery", { scroll: false })
  }

  const handleSubmit = async () => {
    if (!selectedOption) return

    setIsLoading(true)
    try {
      await setShippingMethod(selectedOption)
      router.push(pathname + "?step=payment", { scroll: false })
    } catch (err: any) {
      setError(err.message || err.toString())
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (value: string) => {
    setSelectedOption(value)
  }

  useEffect(() => {
    setIsLoading(false)
    setError(null)
    // Set initial selected option when component opens
    setSelectedOption(cart?.shippingMethods?.[0]?.shippingOption?.id || null)
  }, [isOpen, cart?.shippingMethods])

  return (
    <div className="bg-background">
      <div className="flex flex-row items-center justify-between mb-6">
        <h2 // Use h2
          className={cn( 
            "flex flex-row text-3xl font-medium gap-x-2 items-baseline", // Use Tailwind class
            {
              "opacity-50 pointer-events-none select-none":
                !isOpen && cart?.shippingMethods?.length === 0, 
            }
          )}
        >
          Delivery
          {!isOpen && (cart?.shippingMethods?.length ?? 0) > 0 && ( 
            <CircleCheck className="h-5 w-5" /> 
          )}
        </h2>
        {!isOpen &&
          cart?.shippingAddress && 
          cart?.billingAddress && 
          cart?.email && (
            <p>
              <Button
                onClick={handleEdit}
                data-testid="edit-delivery-button"
                variant="outline"
                size="sm"
              >
                Update Delivery
              </Button>
            </p>
          )}
      </div>
      {isOpen ? (
        <>
          <div className="grid">
            <div className="flex flex-col">
              <span className="font-medium text-sm text-foreground">
                Shipping method
              </span>
              <span className="mb-4 text-muted-foreground text-sm font-normal">
                How would you like your order delivered
              </span>
            </div>
            <div data-testid="delivery-options-container">
              <div className="pb-6 md:pt-0 pt-2">
                {/* Removed pickup RadioGroup */}
                <RadioGroup
                  value={selectedOption}
                  onChange={handleChange}
                >
                  {_shippingMethods?.map((option: any) => {
                    const isDisabled = false

                    return (
                      <Radio
                        key={option.id}
                        value={option.id}
                        data-testid="delivery-option-radio"
                        disabled={isDisabled}
                        className={cn(
                          "flex items-center justify-between text-xs font-normal cursor-pointer py-4 border rounded-md px-8 mb-2",
                          {
                            "border-primary": option.id === selectedOption,
                            "cursor-not-allowed": isDisabled,
                          }
                        )}
                      >
                        <div className="flex items-center gap-x-4">
                          <MedusaRadio
                            checked={option.id === selectedOption}
                          />
                          <span className="text-sm font-normal">
                            {option.name}
                          </span>
                        </div>
                        <span className="justify-self-end text-foreground">
                          {option.calculatedAmount}
                        </span>
                      </Radio>
                    )
                  })}
                </RadioGroup>
              </div>
            </div>
          </div>

          {/* Removed pickup options section */}

          <div>
            <ErrorMessage
              error={error}
              data-testid="delivery-option-error-message"
            />
            <Button
              size="lg" // Map size
              onClick={handleSubmit} // handleSubmit now handles API call + navigation
              disabled={!selectedOption || isLoading} // Disable if no method or loading
              data-testid="submit-delivery-option-button"
            >
              {isLoading && <RiLoader2Fill className="mr-2 h-4 w-4 animate-spin" />} 
              Continue to payment
            </Button>
          </div>
        </>
      ) : (
        <div>
          <div className="text-xs font-normal"> 
            {/* Updated summary section */}
            {cart && (cart?.shippingMethods?.length ?? 0) > 0 && (
              <div className="flex flex-col w-1/3">
                <p className="text-sm font-medium text-foreground mb-1"> 
                  Method
                </p>
                <p className="text-sm font-normal text-muted-foreground"> 
                {cart.shippingMethods[0].shippingOption.name} ({cart.shipping})
                </p>
              </div>
            )}
          </div>
        </div>
      )}
      <Divider className="mt-8" />
    </div>
  )
}

export default Shipping
