import { Text } from "@medusajs/ui"

const LineItemOptions = ({
  variant,
  variantTitle
}) => {
  return (
    <div className="text-muted-foreground">
      {variantTitle && (
        <Text className="inline-block w-full overflow-hidden text-ellipsis">
          {variantTitle}
        </Text>
      )}
      {variant?.sku && (
        <Text className="inline-block w-full overflow-hidden text-ellipsis">
          SKU: {variant.sku}
        </Text>
      )}
    </div>
  );
}

export default LineItemOptions
