import { Label } from "@/components/ui/label" // Shadcn Label
import { Input as ShadcnInput } from "@/components/ui/input" // Shadcn Input, aliased to avoid name clash
import { cn } from "@/lib/utils/cn"
import React, { useEffect, useImperativeHandle, useState } from "react"

import Eye from "@/features/storefront/modules/common/icons/eye"
import EyeOff from "@/features/storefront/modules/common/icons/eye-off"

type InputProps = Omit<
  Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
  "placeholder"
> & {
  label: string
  errors?: Record<string, unknown>
  touched?: Record<string, unknown>
  name: string
  topLabel?: string
  placeholder?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ type, name, label, touched, required, topLabel, placeholder, ...props }, ref) => {
    const inputRef = React.useRef<HTMLInputElement>(null)
    const [showPassword, setShowPassword] = useState(false)
    const [inputType, setInputType] = useState(type)

    useEffect(() => {
      if (type === "password" && showPassword) {
        setInputType("text")
      }

      if (type === "password" && !showPassword) {
        setInputType("password")
      }
    }, [type, showPassword])

    useImperativeHandle(ref, () => inputRef.current!)

    return (
      <div className="flex flex-col w-full">
        {topLabel && (
          <Label className="mb-2 text-xs font-medium leading-5">{topLabel}</Label>
        )}
        <div className="flex relative z-0 w-full text-sm font-normal leading-5"> 
          <ShadcnInput // Use Shadcn Input
            type={inputType}
            name={name}
            placeholder={placeholder || label} // Use provided placeholder or label
            required={required}
            // Apply original padding/height for floating label, replace Medusa classes with Tailwind/Shadcn defaults
            className="block w-full appearance-none rounded-md border border-border bg-background px-4 pb-1 pt-4 focus:outline-none focus:ring-0 hover:bg-accent" // Adjusted classes
            {...props}
            ref={inputRef}
          />
          <label
            htmlFor={name}
            onClick={() => inputRef.current?.focus()}
            className="flex items-center justify-center mx-3 px-1 transition-all absolute duration-300 top-3 -z-1 origin-0 text-muted-foreground" // Use Tailwind color
          >
            {label}
            {required && <span className="text-rose-500">*</span>}
          </label>
          {type === "password" && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-muted-foreground px-4 focus:outline-none transition-all duration-150 outline-none focus:text-foreground absolute right-0 top-3" // Use Tailwind colors
            >
              {showPassword ? <Eye /> : <EyeOff />}
            </button>
          )}
        </div>
      </div>
    )
  }
)

Input.displayName = "Input"

export default Input
