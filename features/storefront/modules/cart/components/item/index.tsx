"use client";
import { Table, Text, clx } from "@medusajs/ui";

import CartItemSelect from "@/features/storefront/modules/cart/components/cart-item-select";
import DeleteButton from "@/features/storefront/modules/common/components/delete-button";
import LineItemOptions from "@/features/storefront/modules/common/components/line-item-options";
import LineItemPrice from "@/features/storefront/modules/common/components/line-item-price";
import LineItemUnitPrice from "@/features/storefront/modules/common/components/line-item-unit-price";
import Thumbnail from "@/features/storefront/modules/products/components/thumbnail";
import { updateLineItem } from "@/features/storefront/lib/data/cart";
import { useState } from "react";
import ErrorMessage from "@/features/storefront/modules/checkout/components/error-message";
import LocalizedClientLink from "@/features/storefront/modules/common/components/localized-client-link";
import { RiLoader2Fill } from "@remixicon/react";
import { Button } from "@/components/ui/button";

const Item = ({ item, region, type = "full" }) => {
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState(null);

  const { handle } = item.productVariant.product;

  const maxQuantity = Math.min(
    item.productVariant.inventoryQuantity > 0
      ? item.productVariant.inventoryQuantity
      : 10,
    10
  );

  const changeQuantity = async (quantity) => {
    setError(null);
    setUpdating(true);

    const message = await updateLineItem({
      lineId: item.id,
      quantity,
    })
      .catch((err) => {
        return err.message;
      })
      .finally(() => {
        setUpdating(false);
      });

    message && setError(message);
  };

  return (
    <Table.Row className="w-full">
      <Table.Cell className="!pl-0 p-4 w-24">
        <LocalizedClientLink
          href={`/products/${handle}`}
          className={clx("flex", {
            "w-16": type === "preview",
            "sm:w-24 w-12": type === "full",
          })}
        >
          <Thumbnail
            thumbnail={item.productVariant.product.thumbnail}
            size="square"
          />
        </LocalizedClientLink>
      </Table.Cell>

      <Table.Cell className="text-left">
        <Text className="text-base font-semibold">
          {item.title}
        </Text>
        {item.productVariant.title && (
          <p className="text-muted-foreground">{item.productVariant.title}</p>
        )}
        <LineItemOptions
          variant={item.productVariant}
          variantTitle={item.variantTitle}
        />
      </Table.Cell>

      {type === "full" && (
        <Table.Cell>
          <div className="flex gap-2 items-center w-28">
            <CartItemSelect
              value={item.quantity}
              onChange={(e) => changeQuantity(parseInt(e.target.value))}
              min={1}
              max={maxQuantity}
              step={1}
              className="w-20 h-10"
            />
            {updating ? (
              <Button size="icon" variant="outline" disabled>
                <RiLoader2Fill className="animate-spin" />
              </Button>
            ) : (
              <DeleteButton id={item.id} />
            )}
          </div>
          <ErrorMessage error={error} />
        </Table.Cell>
      )}

      {type === "full" && (
        <Table.Cell className="hidden sm:table-cell">
          <LineItemUnitPrice item={item} region={region} style="tight" />
        </Table.Cell>
      )}

      <Table.Cell className="!pr-0">
        <span
          className={clx("!pr-0", {
            "flex flex-col items-end h-full justify-center": type === "preview",
          })}
        >
          {type === "preview" && (
            <span className="flex gap-x-1 text-sm">
              <Text className="text-gray-400">{item.quantity}x </Text>
              <LineItemUnitPrice item={item} region={region} style="tight" />
            </span>
          )}
          <LineItemPrice item={item} region={region} style="tight" />
        </span>
      </Table.Cell>
    </Table.Row>
  );
};

export default Item;
