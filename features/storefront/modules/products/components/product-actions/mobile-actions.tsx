import { Dialog, Transition } from "@headlessui/react"
import { <PERSON><PERSON> } from "@/components/ui/button" // Use Shadcn Button
import { cn } from "@/lib/utils/cn" 
import React, { Fragment, useMemo } from "react"

import useToggleState from "@/features/storefront/lib/hooks/use-toggle-state"
import ChevronDown from "@/features/storefront/modules/common/icons/chevron-down"
import X from "@/features/storefront/modules/common/icons/x"

import { getProductPrice } from "@/features/storefront/lib/util/get-product-price"
import OptionSelect from "./option-select"
// Removed HttpTypes import
import { isSimpleProduct } from "@/features/storefront/lib/util/product"

// Define inline types matching those used in ProductActions
type ProductOptionValueInfo = {
  id: string;
  value: string;
  productOption?: { // Link back to the option - CONSISTENT WITH ProductActions
    id: string;
  } | null;
};

type ProductOptionInfoForActions = {
  id: string;
  title?: string | null;
  name?: string | null;
  productOptionValues: ProductOptionValueInfo[];
};

type ProductVariantInfoForActions = {
  id: string;
  title?: string | null; // Correct field name from schema
  inventoryQuantity?: number | null; // Correct field name from schema (camelCase)
  allowBackorder?: boolean | null; // Correct field name from schema (camelCase)
  productOptionValues?: ProductOptionValueInfo[] | null; // Correct field name from schema - CONSISTENT
  prices?: { amount: number; currencyCode: string }[] | null;
};

type RegionInfoForActions = {
  id: string;
  currencyCode: string;
};

type ProductInfoForActions = {
  id: string;
  title?: string | null; // Correct field name from schema
  productOptions?: ProductOptionInfoForActions[] | null; // Correct field name from schema - CONSISTENT
  productVariants?: ProductVariantInfoForActions[] | null; // Correct field name from schema - CONSISTENT
};

type MobileActionsProps = {
  product: ProductInfoForActions; // Use updated type
  region: RegionInfoForActions; // Use updated type
  variant?: ProductVariantInfoForActions; // Use updated type
  options: Record<string, string | undefined>;
  updateOptions: (optionId: string, value: string) => void; // Changed first arg name to match usage
  inStock?: boolean;
  handleAddToCart: () => void;
  isAdding?: boolean;
  show: boolean;
  optionsDisabled: boolean;
};

const MobileActions: React.FC<MobileActionsProps> = ({
  product,
  region, // Destructure region prop
  variant,
  options,
  updateOptions,
  inStock,
  handleAddToCart,
  isAdding,
  show,
  optionsDisabled,
}) => {
  const { state, open, close } = useToggleState()

  const price = getProductPrice({
    product: product,
    variantId: variant?.id,
    region,
  });

  const selectedPrice = useMemo(() => {
    if (!price) {
      return null
    }
    const { variantPrice, cheapestPrice } = price

    return variantPrice || cheapestPrice || null
  }, [price])

  // Replicate isSimpleProduct logic locally to match ProductInfoForActions type
  const isSimple = useMemo(() => (product.productVariants?.length ?? 0) <= 1, [product.productVariants]); // Use productVariants and handle null/0 cases

  return (
    <>
      <div
        className={cn("lg:hidden inset-x-0 bottom-0 fixed", { 
          "pointer-events-none": !show,
        })}
      >
        <Transition
          as={Fragment}
          show={show}
          enter="ease-in-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div
            className="bg-background flex flex-col gap-y-3 justify-center items-center text-base leading-6 font-normal p-4 h-full w-full border-t"
            data-testid="mobile-actions"
          >
            <div className="flex items-center gap-x-2">
              <span data-testid="mobile-title">{product.title}</span>
              <span>—</span>
              {selectedPrice ? (
                <div className="flex items-end gap-x-2 text-foreground">
                  {selectedPrice.calculatedAmount < selectedPrice.originalAmount && (
                    <p>
                      <span className="line-through text-xs leading-5 font-normal">
                        {new Intl.NumberFormat(region.locale, {
                          style: 'currency',
                          currency: selectedPrice.currencyCode,
                        }).format(selectedPrice.originalAmount / 100)}
                      </span>
                    </p>
                  )}
                  <span
                    className={cn({
                      "text-primary": selectedPrice.calculatedAmount < selectedPrice.originalAmount,
                    })}
                  >
                    {new Intl.NumberFormat(region.locale, {
                      style: 'currency',
                      currency: selectedPrice.currencyCode,
                    }).format(selectedPrice.calculatedAmount / 100)}
                  </span>
                </div>
              ) : (
                <div></div>
              )}
            </div>
            <div className={cn("grid grid-cols-2 w-full gap-x-4", { 
              "!grid-cols-1": isSimple
            })}>
              {!isSimple && <Button
                onClick={open}
                variant="outline" // Use Shadcn outline variant
                className="w-full"
                data-testid="mobile-actions-button"
              >
                <div className="flex items-center justify-between w-full">
                  <span>
                    {variant
                      ? Object.values(options).join(" / ")
                      : "Select Options"}
                  </span>
                  <ChevronDown />
                </div>
              </Button>}
              <Button
                onClick={handleAddToCart}
                disabled={!inStock || !variant || !!isAdding} // Combine disabled logic
                className="w-full"
                data-testid="mobile-cart-button"
              >
                {isAdding
                  ? "Adding..." // Indicate loading state
                  : !variant
                    ? "Select variant"
                    : !inStock
                      ? "Out of stock"
                      : "Add to cart"}
              </Button>
            </div>
          </div>
        </Transition>
      </div>
      <Transition appear show={state} as={Fragment}>
        <Dialog as="div" className="relative z-[75]" onClose={close}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-700 bg-opacity-75 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed bottom-0 inset-x-0">
            <div className="flex min-h-full h-full items-center justify-center text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <Dialog.Panel
                  className="w-full h-full transform overflow-hidden text-left flex flex-col gap-y-3"
                  data-testid="mobile-actions-modal"
                >
                  <div className="w-full flex justify-end pr-6">
                    <button
                      onClick={close}
                      className="bg-background w-12 h-12 rounded-full text-foreground flex justify-center items-center"
                      data-testid="close-modal-button"
                    >
                      <X />
                    </button>
                  </div>
                  <div className="bg-background px-6 py-12">
                    {(product.productVariants?.length ?? 0) > 1 && ( // Use productVariants
                      <div className="flex flex-col gap-y-6">
                        {(product.productOptions || []).map((option) => { // Use productOptions
                          return (
                            <div key={option.id}>
                              <OptionSelect
                                option={option}
                                current={options[option.id]}
                                updateOption={updateOptions}
                                title={option.title || option.name || ''} // Use title or name from new type
                                disabled={optionsDisabled}
                              />
                            </div>
                          )
                        })}
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  )
}

export default MobileActions
