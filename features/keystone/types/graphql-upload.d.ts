// types/graphql-upload.d.ts
declare module 'graphql-upload/processRequest.js' {
  import { IncomingMessage, ServerResponse } from 'http';

  function processRequest(
    request: IncomingMessage,
    response: ServerResponse,
    options?: {
      maxFieldSize?: number;
      maxFileSize?: number;
      maxFiles?: number;
    }
  ): Promise<any>; // The actual return type might be more specific if known

  export default processRequest;
}