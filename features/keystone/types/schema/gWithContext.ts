import { GraphQLSchema, GraphQLObjectType } from 'graphql';
import { KeystoneContext } from '@keystone-6/core/types';

export function gWithContext<C extends KeystoneContext>() {
  const baseGraphQL = {
    schema: new GraphQLSchema({
      query: new GraphQLObjectType({
        name: 'Query',
        fields: {}
      })
    }),
    scalars: {},
    extend: (extensions: any) => ({
      ...baseGraphQL,
      ...extensions
    })
  };

  return baseGraphQL;
}

export namespace gWithContext {
  export type infer<T> = T extends GraphQLSchema ? T : never;
}
