import type { GraphQLResolveInfo } from 'graphql'
import type { KeystoneContext } from './context'
import type { ReactElement } from 'react'
import type { JSONValue } from './utils'
import type { FieldController, FieldProps, CellComponent } from './admin-meta'

export type DatabaseProvider = 'sqlite' | 'postgresql' | 'mysql'

export type GraphQLResolver<Context extends KeystoneContext> = (
  root: any,
  args: any,
  context: Context,
  info: GraphQLResolveInfo
) => any

export type GraphQLSchemaExtension<Context extends KeystoneContext> = {
  typeDefs: string
  resolvers: Record<string, Record<string, GraphQLResolver<Context>>>
}

export interface BaseField {
  path: string
  label: string
  description?: string
  viewsIndex: number
  fieldMeta?: {
    validation?: {
      isRequired?: boolean
      length?: { min: number | null; max: number | null }
      match?: { regex: RegExp; explanation?: string }
    }
    defaultValue?: JSONValue
    isNullable?: boolean
    options?: Array<{ value: JSONValue; label: string }>
    isRequired?: boolean
    min?: number
    max?: number
  }
  itemView?: {
    fieldMode?: 'read' | 'edit' | 'hidden'
    fieldPosition?: string
  }
  listView?: {
    fieldMode?: 'read' | 'hidden'
  }
  createView?: {
    fieldMode?: 'edit' | 'hidden'
  }
  isOrderable?: boolean
  isFilterable?: boolean
}

export interface FilterProps {
  field: BaseField
  value: JSONValue
  onChange: (value: JSONValue) => void
  type: string
}

export interface FieldImplementation<Value = unknown> {
  Field: React.ComponentType<FieldProps<() => FieldController<Value>>>
  Cell?: CellComponent<() => FieldController<Value>>
  Filter?: React.ComponentType<FilterProps>
  controller: (field: BaseField) => FieldController<Value>
}
