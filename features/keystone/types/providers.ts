export interface Address {
  name: string;
  company?: string;
  street1: string;
  street2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone?: string;
  email?: string;
}

export interface Dimensions {
  length: number;
  width: number;
  height: number;
  weight: number;
}

export interface LineItem {
  id: string;
  quantity: number;
  price: number;
  name: string;
}

export interface Order {
  id: string;
  customerEmail: string;
  shippingAddress: Address;
  billingAddress: Address;
  items: LineItem[];
  total: number;
}

export interface ShippingProvider {
  createLabel: (order: Order, rateId: string, dimensions: Dimensions, lineItems: LineItem[]) => Promise<{ labelId: string; trackingNumber: string }>;
  getRates: (order: Order, dimensions: Dimensions) => Promise<Array<{ id: string; service: string; rate: number }>>;
  validateAddress: (address: Address) => Promise<{ valid: boolean; normalized?: Address }>;
  getTrackingInfo: (trackingNumber: string) => Promise<{ status: string; events: Array<{ date: Date; status: string; location: string }> }>;
  voidLabel: (labelId: string) => Promise<boolean>;
}

export interface PaymentProvider {
  createPayment: (cart: { id: string }, amount: number, currency: string) => Promise<{ id: string; clientSecret?: string }>;
  capturePayment: (paymentId: string, amount: number) => Promise<boolean>;
  refundPayment: (paymentId: string, amount: number) => Promise<boolean>;
  cancelPayment: (paymentId: string) => Promise<boolean>;
  getPaymentStatus: (paymentId: string) => Promise<{ status: string; amount: number }>;
  handleWebhook: (event: unknown, headers: Record<string, string>) => Promise<{ status: string }>;
}

export type ProviderFunction = (...args: any[]) => Promise<any>;

export interface ProviderError extends Error {
  code?: string;
  statusCode?: number;
  data?: unknown;
} 