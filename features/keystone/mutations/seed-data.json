{"store": {"name": "Impossible Tees", "defaultCurrencyCode": "USD", "metadata": {}, "swapLinkTemplate": "https://impossibletees.com/swap/{id}", "paymentLinkTemplate": "https://impossibletees.com/pay/{id}", "inviteLinkTemplate": "https://impossibletees.com/invite/{id}"}, "currencies": [{"code": "eur", "symbol": "€", "symbolNative": "€", "name": "Euro"}, {"code": "usd", "symbol": "$", "symbolNative": "$", "name": "US Dollar"}], "countries": [{"iso2": "us", "iso3": "usa", "numCode": 840, "name": "United States", "displayName": "United States"}, {"iso2": "ca", "iso3": "can", "numCode": 124, "name": "Canada", "displayName": "Canada"}, {"iso2": "gb", "iso3": "gbr", "numCode": 826, "name": "United Kingdom", "displayName": "United Kingdom"}, {"iso2": "de", "iso3": "deu", "numCode": 276, "name": "Germany", "displayName": "Germany"}, {"iso2": "dk", "iso3": "dnk", "numCode": 208, "name": "Denmark", "displayName": "Denmark"}, {"iso2": "se", "iso3": "swe", "numCode": 752, "name": "Sweden", "displayName": "Sweden"}, {"iso2": "fr", "iso3": "fra", "numCode": 250, "name": "France", "displayName": "France"}, {"iso2": "es", "iso3": "esp", "numCode": 724, "name": "Spain", "displayName": "Spain"}, {"iso2": "it", "iso3": "ita", "numCode": 380, "name": "Italy", "displayName": "Italy"}], "regions": [{"id": "eu", "name": "Europe", "currency_code": "eur", "tax_rate": 0.21, "payment_providers": ["pp_stripe_stripe", "pp_paypal_paypal", "pp_system_default"], "fulfillment_providers": ["manual"], "countries": ["gb", "de", "dk", "se", "fr", "es", "it"]}, {"id": "na", "name": "North America", "currency_code": "usd", "tax_rate": 0.05, "payment_providers": ["pp_stripe_stripe", "pp_paypal_paypal", "pp_system_default"], "fulfillment_providers": ["manual"], "countries": ["us", "ca"]}], "shipping_options": [{"name": "Standard Shipping", "region_id": "eu", "provider_id": "manual", "data": {"id": "manual-fulfillment"}, "price_type": "flat_rate", "amount": 1000}, {"name": "Express Shipping", "region_id": "eu", "provider_id": "manual", "data": {"id": "manual-fulfillment"}, "price_type": "flat_rate", "amount": 1500}, {"name": "Return Shipping", "region_id": "eu", "provider_id": "manual", "data": {"id": "manual-fulfillment"}, "price_type": "flat_rate", "is_return": true, "amount": 1000}, {"name": "Standard Shipping", "region_id": "na", "provider_id": "manual", "data": {"id": "manual-fulfillment"}, "price_type": "flat_rate", "amount": 800}, {"name": "Express Shipping", "region_id": "na", "provider_id": "manual", "data": {"id": "manual-fulfillment"}, "price_type": "flat_rate", "amount": 1200}, {"name": "Return Shipping", "region_id": "na", "provider_id": "manual", "data": {"id": "manual-fulfillment"}, "price_type": "flat_rate", "is_return": true, "amount": 800}], "products": [{"title": "Penrose Triangle T-Shirt", "categories": [{"id": "pcat_shirts"}], "collections": ["latest-picks", "new-arrivals"], "subtitle": null, "": "Mind-bending Penrose Triangle design on a premium cotton T-shirt. Challenge reality with every wear.", "handle": "penrose-triangle-tshirt", "is_giftcard": false, "weight": 200, "images": ["https://example.com/images/penrose-triangle-tshirt-front.png", "https://example.com/images/penrose-triangle-tshirt-back.png"], "options": [{"title": "Size", "values": ["S", "M", "L", "XL"]}, {"title": "Color", "values": ["Black", "White"]}], "variants": [{"title": "S / Black", "prices": [{"currency_code": "eur", "amount": 2500}, {"currency_code": "usd", "amount": 2800}], "options": [{"value": "S"}, {"value": "Black"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "M / Black", "prices": [{"currency_code": "eur", "amount": 2500}, {"currency_code": "usd", "amount": 2800}], "options": [{"value": "M"}, {"value": "Black"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "L / Black", "prices": [{"currency_code": "eur", "amount": 2700}, {"currency_code": "usd", "amount": 3000}], "options": [{"value": "L"}, {"value": "Black"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "XL / Black", "prices": [{"currency_code": "eur", "amount": 2900}, {"currency_code": "usd", "amount": 3200}], "options": [{"value": "XL"}, {"value": "Black"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "S / White", "prices": [{"currency_code": "eur", "amount": 2500}, {"currency_code": "usd", "amount": 2800}], "options": [{"value": "S"}, {"value": "White"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "M / <PERSON>", "prices": [{"currency_code": "eur", "amount": 2500}, {"currency_code": "usd", "amount": 2800}], "options": [{"value": "M"}, {"value": "White"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "L / White", "prices": [{"currency_code": "eur", "amount": 2700}, {"currency_code": "usd", "amount": 3000}], "options": [{"value": "L"}, {"value": "White"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "XL / White", "prices": [{"currency_code": "eur", "amount": 2900}, {"currency_code": "usd", "amount": 3200}], "options": [{"value": "XL"}, {"value": "White"}], "inventory_quantity": 100, "manage_inventory": true}]}, {"title": "<PERSON><PERSON>'s Staircase Hoodie", "categories": [{"id": "pcat_hoodies"}], "collections": ["trending", "new-arrivals"], "subtitle": null, "": "Wrap yourself in the impossible with our <PERSON><PERSON>'s Staircase hoodie. Cozy and mind-bending.", "handle": "eschers-staircase-hoodie", "is_giftcard": false, "weight": 500, "images": ["https://example.com/images/eschers-staircase-hoodie-front.png", "https://example.com/images/eschers-staircase-hoodie-back.png"], "options": [{"title": "Size", "values": ["S", "M", "L", "XL"]}, {"title": "Color", "values": ["Black", "White"]}], "variants": [{"title": "S / Black", "prices": [{"currency_code": "eur", "amount": 4500}, {"currency_code": "usd", "amount": 5000}], "options": [{"value": "S"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "M / Black", "prices": [{"currency_code": "eur", "amount": 4500}, {"currency_code": "usd", "amount": 5000}], "options": [{"value": "M"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "L / Black", "prices": [{"currency_code": "eur", "amount": 4700}, {"currency_code": "usd", "amount": 5200}], "options": [{"value": "L"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "XL / Black", "prices": [{"currency_code": "eur", "amount": 4900}, {"currency_code": "usd", "amount": 5400}], "options": [{"value": "XL"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "S / White", "prices": [{"currency_code": "eur", "amount": 4500}, {"currency_code": "usd", "amount": 5000}], "options": [{"value": "S"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "M / <PERSON>", "prices": [{"currency_code": "eur", "amount": 4500}, {"currency_code": "usd", "amount": 5000}], "options": [{"value": "M"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "L / White", "prices": [{"currency_code": "eur", "amount": 4700}, {"currency_code": "usd", "amount": 5200}], "options": [{"value": "L"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "XL / White", "prices": [{"currency_code": "eur", "amount": 4900}, {"currency_code": "usd", "amount": 5400}], "options": [{"value": "XL"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}]}, {"title": "Möbius Strip Scarf", "categories": [{"id": "pcat_accessories"}], "collections": ["latest-picks"], "subtitle": null, "": "One-sided warmth with our Möbius Strip scarf. A fashionable twist on infinity.", "handle": "mobius-strip-scarf", "is_giftcard": false, "weight": 150, "images": ["https://example.com/images/mobius-strip-scarf.png"], "options": [{"title": "Color", "values": ["Blue", "Red", "Green"]}], "variants": [{"title": "Blue", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "Blue"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "Red", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "Red"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "Green", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "Green"}], "inventory_quantity": 75, "manage_inventory": true}]}, {"title": "Fibonacci Spiral Crop Top", "categories": [{"id": "pcat_shirts"}], "collections": ["trending"], "subtitle": null, "": "Show off your love for mathematical beauty with our Fibonacci Spiral crop top. Perfect for summer days and math enthusiasts.", "handle": "fibonacci-spiral-crop-top", "is_giftcard": false, "weight": 150, "images": ["https://example.com/images/fibonacci-spiral-crop-top-front.png", "https://example.com/images/fibonacci-spiral-crop-top-back.png"], "options": [{"title": "Size", "values": ["XS", "S", "M", "L"]}, {"title": "Color", "values": ["White", "Black"]}], "variants": [{"title": "XS / White", "prices": [{"currency_code": "eur", "amount": 2200}, {"currency_code": "usd", "amount": 2500}], "options": [{"value": "XS"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "S / White", "prices": [{"currency_code": "eur", "amount": 2200}, {"currency_code": "usd", "amount": 2500}], "options": [{"value": "S"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "M / <PERSON>", "prices": [{"currency_code": "eur", "amount": 2200}, {"currency_code": "usd", "amount": 2500}], "options": [{"value": "M"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "L / White", "prices": [{"currency_code": "eur", "amount": 2200}, {"currency_code": "usd", "amount": 2500}], "options": [{"value": "L"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "XS / Black", "prices": [{"currency_code": "eur", "amount": 2200}, {"currency_code": "usd", "amount": 2500}], "options": [{"value": "XS"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "S / Black", "prices": [{"currency_code": "eur", "amount": 2200}, {"currency_code": "usd", "amount": 2500}], "options": [{"value": "S"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "M / Black", "prices": [{"currency_code": "eur", "amount": 2200}, {"currency_code": "usd", "amount": 2500}], "options": [{"value": "M"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "L / Black", "prices": [{"currency_code": "eur", "amount": 2200}, {"currency_code": "usd", "amount": 2500}], "options": [{"value": "L"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}]}, {"title": "Quantum Entanglement Socks", "categories": [{"id": "pcat_accessories"}], "collections": ["new-arrivals"], "subtitle": null, "": "Keep your feet connected across any distance with our Quantum Entanglement Socks. The perfect gift for science-loving friends.", "handle": "quantum-entanglement-socks", "is_giftcard": false, "weight": 50, "images": ["https://example.com/images/quantum-entanglement-socks.png"], "options": [{"title": "Size", "values": ["S", "M", "L"]}, {"title": "Color", "values": ["Black", "White"]}], "variants": [{"title": "S / White", "prices": [{"currency_code": "eur", "amount": 1500}, {"currency_code": "usd", "amount": 1700}], "options": [{"value": "S"}, {"value": "White"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "M / <PERSON>", "prices": [{"currency_code": "eur", "amount": 1500}, {"currency_code": "usd", "amount": 1700}], "options": [{"value": "M"}, {"value": "White"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "L / White", "prices": [{"currency_code": "eur", "amount": 1500}, {"currency_code": "usd", "amount": 1700}], "options": [{"value": "L"}, {"value": "White"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "S / Black", "prices": [{"currency_code": "eur", "amount": 1500}, {"currency_code": "usd", "amount": 1700}], "options": [{"value": "S"}, {"value": "Black"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "M / Black", "prices": [{"currency_code": "eur", "amount": 1500}, {"currency_code": "usd", "amount": 1700}], "options": [{"value": "M"}, {"value": "Black"}], "inventory_quantity": 100, "manage_inventory": true}, {"title": "L / Black", "prices": [{"currency_code": "eur", "amount": 1500}, {"currency_code": "usd", "amount": 1700}], "options": [{"value": "L"}, {"value": "Black"}], "inventory_quantity": 100, "manage_inventory": true}]}, {"title": "Schrödinger's Cat Tank Top", "categories": [{"id": "pcat_shirts"}], "collections": ["latest-picks", "new-arrivals"], "subtitle": null, "": "Is the cat alive or dead? Both! This stylish tank top features <PERSON><PERSON><PERSON><PERSON><PERSON>'s famous thought experiment.", "handle": "schrodingers-cat-tank-top", "is_giftcard": false, "weight": 180, "images": ["https://example.com/images/schrodingers-cat-tank-top-front.png", "https://example.com/images/schrodingers-cat-tank-top-back.png"], "options": [{"title": "Size", "values": ["XS", "S", "M", "L", "XL"]}, {"title": "Color", "values": ["Black", "White"]}], "variants": [{"title": "XS / White", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "XS"}, {"value": "White"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "S / White", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "S"}, {"value": "White"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "M / <PERSON>", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "M"}, {"value": "White"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "L / White", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "L"}, {"value": "White"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "XL / White", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "XL"}, {"value": "White"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "XS / Black", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "XS"}, {"value": "Black"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "S / Black", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "S"}, {"value": "Black"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "M / Black", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "M"}, {"value": "Black"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "L / Black", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "L"}, {"value": "Black"}], "inventory_quantity": 75, "manage_inventory": true}, {"title": "XL / Black", "prices": [{"currency_code": "eur", "amount": 2000}, {"currency_code": "usd", "amount": 2200}], "options": [{"value": "XL"}, {"value": "Black"}], "inventory_quantity": 75, "manage_inventory": true}]}, {"title": "<PERSON>ie", "categories": [{"id": "pcat_accessories"}], "collections": ["trending", "new-arrivals"], "subtitle": null, "": "Keep your head warm with our topologically challenging Klein Bottle beanie. It's a conversation starter and a mind-bender.", "handle": "klein-bottle-beanie", "is_giftcard": false, "weight": 100, "images": ["https://example.com/images/klein-bottle-beanie.png"], "options": [{"title": "Size", "values": ["One Size"]}, {"title": "Color", "values": ["Black", "White", "<PERSON>"]}], "variants": [{"title": "One Size / Black", "prices": [{"currency_code": "eur", "amount": 1800}, {"currency_code": "usd", "amount": 2000}], "options": [{"value": "One Size"}, {"value": "Black"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "One Size / White", "prices": [{"currency_code": "eur", "amount": 1800}, {"currency_code": "usd", "amount": 2000}], "options": [{"value": "One Size"}, {"value": "White"}], "inventory_quantity": 50, "manage_inventory": true}, {"title": "One Size / Gray", "prices": [{"currency_code": "eur", "amount": 1800}, {"currency_code": "usd", "amount": 2000}], "options": [{"value": "One Size"}, {"value": "<PERSON>"}], "inventory_quantity": 50, "manage_inventory": true}]}, {"title": "Paradox Puzzle Sweater", "categories": [{"id": "pcat_hoodies"}], "collections": ["latest-picks", "trending"], "subtitle": null, "": "Challenge your mind and stay warm with our Paradox Puzzle Sweater. Can you solve the unsolvable pattern?", "handle": "paradox-puzzle-sweater", "is_giftcard": false, "weight": 400, "images": ["https://example.com/images/paradox-puzzle-sweater-front.png", "https://example.com/images/paradox-puzzle-sweater-back.png"], "options": [{"title": "Size", "values": ["S", "M", "L", "XL"]}, {"title": "Color", "values": ["Black", "White"]}], "variants": [{"title": "S / Black", "prices": [{"currency_code": "eur", "amount": 3500}, {"currency_code": "usd", "amount": 3900}], "options": [{"value": "S"}, {"value": "Black"}], "inventory_quantity": 30, "manage_inventory": true}, {"title": "M / <PERSON>", "prices": [{"currency_code": "eur", "amount": 3500}, {"currency_code": "usd", "amount": 3900}], "options": [{"value": "M"}, {"value": "White"}], "inventory_quantity": 30, "manage_inventory": true}]}, {"title": "<PERSON><PERSON><PERSON>'s Uncertainty Principle Jo<PERSON>s", "categories": [{"id": "pcat_pants"}], "collections": ["new-arrivals"], "subtitle": null, "": "You can know how comfortable these joggers are, or where they are, but not both at the same time. Quantum fashion at its finest.", "handle": "heisenberg-uncertainty-joggers", "is_giftcard": false, "weight": 300, "images": ["https://example.com/images/heisenberg-uncertainty-joggers-front.png", "https://example.com/images/heisenberg-uncertainty-joggers-back.png"], "options": [{"title": "Size", "values": ["S", "M", "L", "XL"]}, {"title": "Color", "values": ["Black", "<PERSON>"]}], "variants": [{"title": "S / Black", "prices": [{"currency_code": "eur", "amount": 3000}, {"currency_code": "usd", "amount": 3300}], "options": [{"value": "S"}, {"value": "Black"}], "inventory_quantity": 40, "manage_inventory": true}, {"title": "M / Gray", "prices": [{"currency_code": "eur", "amount": 3000}, {"currency_code": "usd", "amount": 3300}], "options": [{"value": "M"}, {"value": "<PERSON>"}], "inventory_quantity": 40, "manage_inventory": true}]}, {"title": "Mandelbrot Set Infinity Shawl", "categories": [{"id": "pcat_accessories"}], "collections": ["trending"], "subtitle": null, "": "Wrap yourself in the beauty of fractals with our Mandelbrot Set Infinity Shawl. Infinitely stylish!", "handle": "mandelbrot-set-infinity-shawl", "is_giftcard": false, "weight": 120, "images": ["https://example.com/images/mandelbrot-set-infinity-scarf.png"], "options": [{"title": "Color", "values": ["Multicolor", "Black and White"]}], "variants": [{"title": "Multicolor", "prices": [{"currency_code": "eur", "amount": 2500}, {"currency_code": "usd", "amount": 2800}], "options": [{"value": "Multicolor"}], "inventory_quantity": 60, "manage_inventory": true}, {"title": "Black and White", "prices": [{"currency_code": "eur", "amount": 2500}, {"currency_code": "usd", "amount": 2800}], "options": [{"value": "Black and White"}], "inventory_quantity": 60, "manage_inventory": true}]}], "categories": [{"id": "pcat_shirts", "name": "Shirts", "rank": 0, "category_children": [], "handle": "shirts"}, {"id": "pcat_hoodies", "name": "Hoodies", "rank": 1, "category_children": [], "handle": "hoodies"}, {"id": "pcat_accessories", "name": "Accessories", "rank": 2, "category_children": [], "handle": "accessories"}, {"id": "pcat_pants", "name": "<PERSON>ts", "rank": 3, "category_children": [], "handle": "pants"}], "collections": [{"id": "pcol_latest", "title": "Latest Picks", "handle": "latest-picks"}, {"id": "pcol_new", "title": "New Arrivals", "handle": "new-arrivals"}, {"id": "pcol_trending", "title": "Trending", "handle": "trending"}], "paymentProviders": [{"name": "Stripe", "code": "pp_stripe_stripe", "isInstalled": true}, {"name": "PayPal", "code": "pp_paypal_paypal", "isInstalled": true}, {"name": "Manual", "code": "pp_system_default", "isInstalled": true}]}