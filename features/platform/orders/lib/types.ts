export interface Dimensions {
  length: string;
  width: string;
  height: string;
  unit: 'in' | 'cm';
}

export interface Weight {
  value: string;
  unit: 'oz' | 'lb' | 'kg';
}

export interface ShippingRate {
  id: string;
  providerId?: string;
  provider: string;
  service: string;
  carrier: string;
  price: string;
  estimatedDays: string;
}

export interface ShippingLabel {
  id: string;
  labelUrl?: string;
  trackingNumber: string;
  trackingUrl?: string;
  carrier?: string;
}

export interface ShippingProvider {
  id: string;
  name: string;
  isActive: boolean;
  metadata?: Record<string, any>;
  createLabelFunction?: string;
  getRatesFunction?: string;
  validateAddressFunction?: string;
  trackShipmentFunction?: string;
  cancelLabelFunction?: string;
}

export interface Address {
  id: string;
  company?: string;
  firstName: string;
  lastName: string;
  address1: string;
  address2?: string;
  city: string;
  province: string;
  postalCode: string;
  phone?: string;
  label?: string;
}

export interface LineItem {
  id: string;
  quantity: number;
  title: string;
  sku?: string;
  thumbnail?: string;
  metadata?: any;
  variantTitle?: string;
  formattedUnitPrice: string;
  formattedTotal: string;
}

export interface FulfillmentItem {
  id: string;
  quantity: number;
  lineItem: LineItem;
}

export interface Fulfillment {
  id: string;
  createdAt: string;
  canceledAt?: string;
  items: FulfillmentItem[];
  shippingLabels?: ShippingLabel[];
}

export interface Order {
  id: string;
  displayId: string;
  createdAt: string;
  status: string;
  email: string;
  unfulfilled: LineItem[];
  fulfillmentDetails?: Fulfillment[];
  lineItems: LineItem[];
  metadata?: Record<string, any>;
  paymentDetails?: Array<{
    status: string;
    paymentLink?: string;
  }>;
  shippingProviders?: ShippingProvider[];
}

export interface UnfulfilledItemProps {
  item: LineItem;
  selected: boolean;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
}

export interface UnfulfilledItemsProps {
  items: LineItem[];
  selectedQuantities: Record<string, string>;
  setSelectedQuantities: (quantities: Record<string, string>) => void;
  order: Order;
}

export interface FulfillmentHistoryProps {
  fulfillments: Fulfillment[];
  order: Order;
  onDelete: (id: string) => void;
}

export interface ShippingTabsProps {
  providers: ShippingProvider[];
  order: Order;
  onRateSelect: (rate: ShippingRate | null) => void;
  onProviderToggle: (providerId: string) => void;
  selectedQuantities: Record<string, string>;
  setSelectedQuantities: (quantities: Record<string, string>) => void;
  refetchProviders: () => void;
  isLoading: boolean;
  selectedRate: ShippingRate | null;
  onCreateLabel: (input: { dimensions: { length: number; width: number; height: number; weight: number; unit: 'in' | 'cm'; weightUnit: 'oz' | 'lb' | 'kg'; } }) => void;
  onCreateFulfillment: (input: { trackingNumber?: string; carrier?: string; noNotification?: boolean; }) => void;
}