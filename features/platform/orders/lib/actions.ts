'use server';

import { revalidatePath } from 'next/cache';
import type { Dimensions, Weight } from './types';
import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';

interface CreateFulfillmentInput {
  orderId: string;
  lineItems: {
    lineItemId: string;
    quantity: number;
  }[];
  trackingNumber?: string;
  carrier?: string;
  noNotification?: boolean;
}

interface CreateLabelInput {
  orderId: string;
  providerId: string;
  rateId: string;
  dimensions: {
    length: number;
    width: number;
    height: number;
    weight: number;
    unit: Dimensions['unit'];
    weightUnit: Weight['unit'];
  };
  lineItems: {
    lineItemId: string;
    quantity: number;
  }[];
}

export async function getShippingProviders() {
  const query = `
    query GetShippingProviders {
      shippingProviders(orderBy: [
        { isActive: desc },
        { name: asc }
      ]) {
        id
        name
        isActive
        accessToken
        fromAddress {
          id
          firstName
          lastName
          company
          address1
          address2
          city
          province
          postalCode
          country {
            iso2
          }
          phone
        }
      }
    }
  `;

  const response = await keystoneClient(query);
  // The KeystoneResponse object is returned directly.
  // Calling code needs to handle the success/error cases.
  return response;
}

export async function createFulfillment(input: CreateFulfillmentInput) {
  const { orderId, lineItems, trackingNumber, carrier, noNotification } = input;

  const mutation = `
    mutation CreateFulfillment(
      $orderId: ID!
      $lineItems: [FulfillmentItemCreateInput!]!
      $trackingNumber: String
      $carrier: String
      $noNotification: Boolean
    ) {
      createFulfillment(data: {
        order: { connect: { id: $orderId } }
        fulfillmentItems: {
          create: $lineItems
        }
        shippingLabels: ${trackingNumber && carrier ? `{
          create: [{
            status: "purchased"
            carrier: $carrier
            trackingNumber: $trackingNumber
            trackingUrl: ${getTrackingUrl(carrier, trackingNumber)}
            metadata: {
              source: "admin"
            }
          }]
        }` : 'null'}
        noNotification: $noNotification
        metadata: {
          source: "admin"
          createdBy: "admin"
          items: $lineItems
        }
      }) {
        id
      }
    }
  `;

  const response = await keystoneClient(mutation, {
    orderId,
    lineItems: lineItems.map(({ lineItemId, quantity }) => ({
      lineItem: { connect: { id: lineItemId } },
      quantity,
    })),
    trackingNumber,
    carrier,
    noNotification,
  });

  // Revalidate the path only on success
  if (response.success) {
    revalidatePath(`/dashboard/platform/orders/${orderId}`);
  }

  // Return the entire KeystoneResponse object
  return response;
}

// Helper function to generate tracking URLs
function getTrackingUrl(carrier: string, trackingNumber: string): string {
  switch (carrier?.toLowerCase()) {
    case 'ups':
      return `https://www.ups.com/track?tracknum=${trackingNumber}`;
    case 'usps':
      return `https://tools.usps.com/go/TrackConfirmAction?tLabels=${trackingNumber}`;
    case 'fedex':
      return `https://www.fedex.com/fedextrack/?trknbr=${trackingNumber}`;
    case 'dhl':
      return `https://www.dhl.com/en/express/tracking.html?AWB=${trackingNumber}`;
    default:
      return '';
  }
}

export async function createShippingLabel(input: CreateLabelInput) {
  const { orderId, providerId, rateId, dimensions, lineItems } = input;

  const mutation = `
    mutation CreateProviderShippingLabel(
      $orderId: ID!
      $providerId: ID!
      $rateId: String!
      $dimensions: DimensionsInput
      $lineItems: [LineItemInput!]
    ) {
      createProviderShippingLabel(
        orderId: $orderId
        providerId: $providerId
        rateId: $rateId
        dimensions: $dimensions
        lineItems: $lineItems
      ) {
        id
        status
        trackingNumber
        trackingUrl
        labelUrl
        data
      }
    }
  `;

  const response = await keystoneClient(mutation, {
    orderId,
    providerId,
    rateId,
    dimensions,
    lineItems
  });

  // Revalidate the path only on success
  if (response.success) {
    revalidatePath(`/dashboard/platform/orders/${orderId}`);
  }

  // Return the entire KeystoneResponse object
  return response;
}

export async function getRatesForOrder(orderId: string, providerId: string, dimensions: any) {
  const mutation = `
    mutation GetRatesForOrder(
      $orderId: ID!
      $providerId: ID!
      $dimensions: DimensionsInput
    ) {
      getRatesForOrder(
        orderId: $orderId
        providerId: $providerId
        dimensions: $dimensions
      ) {
        id
        provider
        service
        carrier
        price
        estimatedDays
      }
    }
  `;

  const response = await keystoneClient(mutation, {
    orderId,
    providerId,
    dimensions
  });

  // Return the entire KeystoneResponse object
  // Calling code needs to handle the success/error cases.
  return response;
}

export async function toggleProvider(providerId: string) {
  const mutation = `
    mutation ToggleProvider($id: ID!) {
      updateShippingProvider(
        where: { id: $id }
        data: {
          isActive: { set: false }
        }
      ) {
        id
        isActive
      }
    }
  `;

  const response = await keystoneClient(mutation, { id: providerId });

  // Revalidate the path only on success
  if (response.success) {
    // Note: The path '/dashboard/platform/orders/[id]' seems generic.
    // Consider if a more specific path is needed for revalidation.
    revalidatePath('/dashboard/platform/orders/[id]');
  }

  // Return the entire KeystoneResponse object
  return response;
}

export async function deleteProvider(providerId: string) {
  const mutation = `
    mutation DeleteProvider($id: ID!) {
      deleteShippingProvider(where: { id: $id }) {
        id
      }
    }
  `;

  const response = await keystoneClient(mutation, { id: providerId });

  // Revalidate the path only on success
  if (response.success) {
    // Note: The path '/dashboard/platform/orders/[id]' seems generic.
    // Consider if a more specific path is needed for revalidation.
    revalidatePath('/dashboard/platform/orders/[id]');
  }

  // Return the entire KeystoneResponse object
  return response;
}

export interface CreateShippingProviderInput {
  name: string;
  accessToken: string;
  fromAddressId: string;
  createLabelFunction?: string;
  getRatesFunction?: string;
  validateAddressFunction?: string;
  trackShipmentFunction?: string;
  cancelLabelFunction?: string;
  metadata?: Record<string, any>;
}

export async function createShippingProvider(input: CreateShippingProviderInput) {
  const {
    name,
    accessToken,
    fromAddressId,
    createLabelFunction,
    getRatesFunction,
    validateAddressFunction,
    trackShipmentFunction,
    cancelLabelFunction,
    metadata
  } = input;

  const mutation = `
    mutation CreateShippingProvider(
      $name: String!
      $accessToken: String!
      $fromAddressId: ID!
      $createLabelFunction: String
      $getRatesFunction: String
      $validateAddressFunction: String
      $trackShipmentFunction: String
      $cancelLabelFunction: String
      $metadata: JSON
    ) {
      createShippingProvider(data: {
        name: $name
        accessToken: $accessToken
        fromAddress: { connect: { id: $fromAddressId } }
        isActive: true
        createLabelFunction: $createLabelFunction
        getRatesFunction: $getRatesFunction
        validateAddressFunction: $validateAddressFunction
        trackShipmentFunction: $trackShipmentFunction
        cancelLabelFunction: $cancelLabelFunction
        metadata: $metadata
      }) {
        id
        name
        isActive
        metadata
      }
    }
  `;

  const response = await keystoneClient(mutation, {
    name,
    accessToken,
    fromAddressId,
    createLabelFunction,
    getRatesFunction,
    validateAddressFunction,
    trackShipmentFunction,
    cancelLabelFunction,
    metadata
  });

  // Revalidate the path only on success
  if (response.success) {
    // Note: The path '/dashboard/platform/orders/[id]' seems generic.
    // Consider if a more specific path is needed for revalidation.
    revalidatePath('/dashboard/platform/orders/[id]');
  }

  // Return the entire KeystoneResponse object
  return response;
}

export async function cancelFulfillment(fulfillmentId: string) {
  const mutation = `
    mutation CancelFulfillment($id: ID!, $data: FulfillmentUpdateInput!) {
      updateFulfillment(
        where: { id: $id }
        data: $data
      ) {
        id
        canceledAt
      }
    }
  `;

  const response = await keystoneClient(mutation, { 
    id: fulfillmentId,
    data: {
      canceledAt: new Date().toISOString()
    }
  });

  if (response.success) {
    revalidatePath('/dashboard/platform/orders/[id]');
  }

  return response;
}

export async function getCurrentUser() {
  const query = `
    query GetCurrentUser {
      authenticatedItem {
        ... on User {
          id
          name
          email
        }
      }
    }
  `;

  const response = await keystoneClient(query);

  // Return the entire KeystoneResponse object
  // Calling code needs to handle the success/error cases.
  return response;
}

export async function getAddresses(userId: string, limit: number = 10, skip: number = 0) {
  const query = `
    query GetUserAddresses($userId: ID!, $take: Int!, $skip: Int!) {
      addresses(
        where: { user: { id: { equals: $userId } } }
        take: $take
        skip: $skip
      ) {
        id
        company
        firstName
        lastName
        address1
        address2
        city
        province
        postalCode
        phone
        country {
          id
          name
        }
      }
      addressesCount(where: { user: { id: { equals: $userId } } })
    }
  `;

  const response = await keystoneClient(query, {
    userId,
    take: limit,
    skip
  });

  // Return the entire KeystoneResponse object
  // Calling code needs to handle the success/error cases and extract data/total.
  return response;
}

export async function getCountries() {
  const query = `
    query GetCountries {
      countries(orderBy: { name: asc }) {
        id
        name
        iso2
      }
    }
  `;

  const response = await keystoneClient(query);

  // Return the entire KeystoneResponse object
  // Calling code needs to handle the success/error cases.
  return response;
}

export async function createAddress(input: {
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  province: string;
  postalCode: string;
  phone?: string;
  country: string;
}) {
  const { country, ...rest } = input;

  const mutation = `
    mutation CreateAddress($data: AddressCreateInput!) {
      createAddress(data: $data) {
        id
        firstName
        lastName
        company
        address1
        address2
        city
        province
        postalCode
        phone
        country {
          id
          name
        }
      }
    }
  `;

  // Get the current user
  const currentUserResponse = await getCurrentUser();
  if (!currentUserResponse.success) {
    // Propagate the error as a KeystoneResponse
    console.error('Failed to get current user:', currentUserResponse.error);
    return { success: false, error: currentUserResponse.error };
  }
  const currentUser = currentUserResponse.data.authenticatedItem;
  if (!currentUser?.id) {
    // Handle case where user is technically authenticated but no ID is found (shouldn't happen ideally)
    console.error('Authenticated user has no ID.');
    return { success: false, error: 'Authenticated user has no ID' };
  }

  const response = await keystoneClient(mutation, {
    data: {
      ...rest,
      user: { connect: { id: currentUser.id } },
      country: { connect: { id: country } }
    }
  });

  // The calling component now expects the full KeystoneResponse
  return response;
}

export async function getOrder(orderId: string) {
  const query = `
    query ($id: ID!) {
      order(where: { id: $id }) {
        id
        displayId
        status
        fulfillmentStatus
        fulfillmentDetails
        total
        subtotal
        shipping
        tax
        note
        currency {
          code
          symbol
          name
        }
        metadata
        email
        createdAt
        updatedAt
        canceledAt
        paymentDetails
        totalPaid
        formattedTotalPaid
        lineItems {
          id
          quantity
          title
          sku
          thumbnail
          metadata
          variantTitle
          formattedUnitPrice
          formattedTotal
          productData
          variantData
        }
        fulfillments {
          id
          createdAt
          fulfillmentItems {
            id
            quantity
            lineItem {
              id
              quantity
              title
              sku
              thumbnail
              metadata
              variantTitle
              formattedUnitPrice
              formattedTotal
              productData
              variantData
            }
          }
          shippingLabels {
            id
            labelUrl
            trackingNumber
            trackingUrl
            carrier
          }
        }
        unfulfilled
        user {
          id
          name
          email
          phone
          orders {
            id
          }
        }
        billingAddress {
          id
          firstName
          lastName
          company
          address1
          address2
          city
          province
          postalCode
          phone
          country {
            name
          }
        }
        shippingAddress {
          id
          firstName
          lastName
          company
          address1
          address2
          city
          province
          postalCode
          phone
          country {
            name
          }
        }
        events {
          id
          type
          data
          time
          createdAt
          user {
            id
            name
            email
          }
        }
      }
    }
  `;

  const cacheOptions = {
    next: {
      tags: [`order-${orderId}`],
      revalidate: 3600, // Cache for 1 hour
    },
  };

  const response = await keystoneClient(query, { id: orderId }, cacheOptions);
  // Return the entire KeystoneResponse object
  // Calling code needs to handle the success/error cases.
  return response;
}

export async function createProviderShippingLabel({
  orderId,
  providerId,
  rateId,
  dimensions,
  lineItems,
}: {
  orderId: string;
  providerId: string;
  rateId: string;
  dimensions: {
    length: number;
    width: number;
    height: number;
    weight: number;
    unit: 'in' | 'cm';
    weightUnit: 'oz' | 'lb' | 'kg';
  };
  lineItems: { lineItemId: string; quantity: number }[];
}) {
  const mutation = `
    mutation CreateProviderShippingLabel(
      $orderId: ID!
      $providerId: ID!
      $rateId: String!
      $dimensions: DimensionsInput
      $lineItems: [LineItemInput!]
    ) {
      createProviderShippingLabel(
        orderId: $orderId
        providerId: $providerId
        rateId: $rateId
        dimensions: $dimensions
        lineItems: $lineItems
      ) {
        id
        status
        trackingNumber
        trackingUrl
        labelUrl
        data
      }
    }
  `;

  const response = await keystoneClient(mutation, {
    orderId,
    providerId,
    rateId,
    dimensions,
    lineItems
  });

  // Revalidate the path only on success
  if (response.success) {
    revalidatePath(`/platform/orders/${orderId}`);
    revalidatePath(`/platform/orders/${orderId}/fulfill`);
  }

  return response;
}

export async function createManualFulfillment({
  orderId,
  lineItems,
  trackingNumber,
  carrier,
  noNotification,
}: {
  orderId: string;
  lineItems: { lineItemId: string; quantity: number }[];
  trackingNumber?: string;
  carrier?: string;
  noNotification?: boolean;
}) {
  const mutation = `
    mutation CreateFulfillment(
      $orderId: ID!
      $lineItems: [FulfillmentItemCreateInput!]!
      $trackingNumber: String
      $carrier: String
      $noNotification: Boolean
    ) {
      createFulfillment(data: {
        order: { connect: { id: $orderId } }
        fulfillmentItems: {
          create: $lineItems
        }
        shippingLabels: ${trackingNumber && carrier ? `{
          create: [{
            status: "purchased"
            carrier: $carrier
            trackingNumber: $trackingNumber
            trackingUrl: ${getTrackingUrl(carrier, trackingNumber)}
            metadata: {
              source: "admin"
            }
          }]
        }` : 'null'}
        noNotification: $noNotification
        metadata: {
          source: "admin"
          createdBy: "admin"
          items: $lineItems
        }
      }) {
        id
        shippingLabels {
          id
          status
          trackingNumber
          trackingUrl
          labelUrl
          carrier
          data
        }
      }
    }
  `;

  const response = await keystoneClient(mutation, {
    orderId,
    lineItems: lineItems.map(({ lineItemId, quantity }) => ({
      lineItem: { connect: { id: lineItemId } },
      quantity,
    })),
    trackingNumber,
    carrier,
    noNotification,
  });

  // Revalidate the path only on success
  if (response.success) {
    revalidatePath(`/platform/orders/${orderId}`);
    revalidatePath(`/platform/orders/${orderId}/fulfill`);
  }

  return response;
}