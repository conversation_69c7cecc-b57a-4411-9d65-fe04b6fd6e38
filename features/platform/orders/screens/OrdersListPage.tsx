import { getListByPath } from "@/features/dashboard/actions"; 
import { getFilteredOrders, getOrderStatusCounts } from "@/features/platform/orders/actions";
import { PageBreadcrumbs } from "@/features/dashboard/components/PageBreadcrumbs";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Circle, Square, Triangle } from "lucide-react";
import { OrderDetailsComponent } from "../components/OrderDetailsComponent";
import { StatusTabs } from "../components/StatusTabs";
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import type { SortOption } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from "@/features/dashboard/components/PaginationWrapper";

// Define Order type
interface Order {
  id: string;
  displayId: string;
  status: string;
  createdAt: string;
  total: string;
  user?: {
    id: string;
    name?: string;
    email: string;
  };
  shippingAddress?: {
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string;
    city: string;
    province: string;
    postalCode: string;
    phone?: string;
  };
  lineItems?: Array<{
    id: string;
    title: string;
    quantity: number;
    sku?: string;
    thumbnail?: string;
    formattedUnitPrice?: string;
    formattedTotal?: string;
    variantData?: any;
    productData?: any;
  }>;
}

interface PageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

function ErrorDisplay({ title, message }: { title: string; message: string }) {
  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-2xl font-bold tracking-tight text-red-600">
        {title}
      </h1>
      <p className="mt-2 text-gray-600">{message}</p>
    </div>
  );
}

export async function OrdersListPage({ searchParams }: PageProps) {
  const resolvedSearchParams = await searchParams;
  // Parse search parameters
  const page = Number(resolvedSearchParams.page) || 1;
  const pageSize = Number(resolvedSearchParams.pageSize) || 10;

  // Get status filter from URL
  let status = null;
  const statusFilter = resolvedSearchParams["!status_matches"];
  if (statusFilter) {
    try {
      const parsed = JSON.parse(decodeURIComponent(statusFilter as string));
      if (Array.isArray(parsed) && parsed.length > 0) {
        status = parsed[0].value;
      }
    } catch (e) {
      // Invalid JSON in URL, ignore
    }
  }

  // Get search term from URL
  const search = typeof resolvedSearchParams.search === "string" && resolvedSearchParams.search !== "" ? resolvedSearchParams.search : null;

  try {
    // Get list metadata
    const list = await getListByPath("orders");

    if (!list) {
      return (
        <ErrorDisplay
          title="Invalid List"
          message="The requested list could not be found."
        />
      );
    }

    // Get sort from URL
    const sortBy = resolvedSearchParams.sortBy as string | undefined;
    const sort = sortBy ? {
      field: sortBy.startsWith("-") ? sortBy.slice(1) : sortBy,
      direction: sortBy.startsWith("-") ? "DESC" : "ASC"
    } as SortOption : null;

    // Fetch orders with filters
    const response = await getFilteredOrders(
      status,
      search,
      page,
      pageSize,
      sort
    );

    let orders: Order[] = [];
    let count = 0;

    if (response.success) {
      // Ensure data exists and has the expected properties
      orders = response.data?.items || [];
      count = response.data?.count || 0;
    } else {
      // Log the error and use fallback values
      console.error("Error fetching orders:", response.error);
      // orders and count are already initialized to fallback values
    }

    // Get status counts
    const statusCountsResponse = await getOrderStatusCounts();
    let statusCounts = {
      pending: 0,
      requires_action: 0,
      completed: 0,
      archived: 0,
      canceled: 0,
      all: 0,
    };

    if (statusCountsResponse.success) {
      statusCounts = statusCountsResponse.data;
    } else {
      console.error("Error fetching order status counts:", statusCountsResponse.error);
      // statusCounts already initialized with default 0 values
    }

    return (
      <section
        aria-label="Orders overview"
        className="overflow-hidden flex flex-col"
      >
        <PageBreadcrumbs
          items={[
            {
              type: "link",
              label: "Dashboard",
              href: "/",
            },
            {
              type: "page",
              label: "Platform",
              showModelSwitcher: true,
              switcherType: "platform",
            },
            {
              type: "page",
              label: "Orders",
            },
          ]}
        />

        <div className="flex flex-col flex-1 min-h-0">

          <div className="border-gray-200 dark:border-gray-800">
            <div className="px-4 md:px-6 pt-4 md:pt-6 pb-4">
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-50">
                Orders
              </h1>
              <p className="text-muted-foreground">
                <span>Create and manage orders</span>
              </p>
            </div>
          </div>

          <PlatformFilterBar
            list={{
              key: list.key,
              path: list.path,
              label: list.label,
              singular: list.singular,
              plural: list.plural,
              description: list.description || undefined,
              labelField: list.labelField as string,
              initialColumns: list.initialColumns,
              groups: list.groups as unknown as string[],
              graphql: {
                plural: list.plural,
                singular: list.singular
              },
              fields: list.fields
            }}
            currentSort={sort}
          />

          <div className="border-b">
            <StatusTabs
              statusCounts={{
                all: statusCounts.all || 0,
                pending: statusCounts.pending || 0,
                requires_action: statusCounts.requires_action || 0,
                completed: statusCounts.completed || 0,
                archived: statusCounts.archived || 0,
                canceled: statusCounts.canceled || 0
              }}
            />
          </div>

          <div className="flex-1 overflow-auto">
            {orders && orders.length > 0 ? (
              <div className="grid grid-cols-1 divide-y">
                {orders.map((order: Order) => (
                  <OrderDetailsComponent key={order.id} order={order} />
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full py-10">
                <div className="text-center">
                  <div className="relative h-11 w-11 mx-auto mb-2">
                    <Triangle className="absolute left-1 top-1 w-4 h-4 fill-indigo-200 stroke-indigo-400 dark:stroke-indigo-600 dark:fill-indigo-950 rotate-[90deg]" />
                    <Square className="absolute right-[.2rem] top-1 w-4 h-4 fill-orange-300 stroke-orange-500 dark:stroke-amber-600 dark:fill-amber-950 rotate-[30deg]" />
                    <Circle className="absolute bottom-2 left-1/2 -translate-x-1/2 w-4 h-4 fill-emerald-200 stroke-emerald-400 dark:stroke-emerald-600 dark:fill-emerald-900" />
                  </div>
                  <p className="font-medium">No orders found</p>
                  <p className="text-muted-foreground text-sm">
                    {(search !== null) || status
                      ? "Try adjusting your search or filter criteria"
                      : "Create your first order to get started"}
                  </p>
                  {((search !== null) || status) && (
                    <Link href="/dashboard/platform/orders">
                      <Button variant="outline" className="mt-4" size="sm">
                        Clear filters
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            )}
          </div>

          <PaginationWrapper
            currentPage={page}
            total={count}
            pageSize={pageSize}
            list={{
              singular: "order",
              plural: "orders",
              path: "orders",
              gqlNames: {
                deleteMutationName: list.gqlNames?.deleteMutationName || '',
                listQueryName: list.gqlNames?.listQueryName || '',
                itemQueryName: list.gqlNames?.itemQueryName || '',
                listQueryCountName: list.gqlNames?.listQueryCountName || '',
                listOrderName: list.gqlNames?.listOrderName || '',
                updateMutationName: list.gqlNames?.updateMutationName || '',
                createMutationName: list.gqlNames?.createMutationName || '',
                whereInputName: list.gqlNames?.whereInputName || '',
                whereUniqueInputName: list.gqlNames?.whereUniqueInputName || '',
                updateInputName: list.gqlNames?.updateInputName || '',
                createInputName: list.gqlNames?.createInputName || ''
              }
            }}
          />
        </div>
      </section>
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return (
      <ErrorDisplay
        title="Error Loading Orders"
        message={`There was an error loading orders: ${errorMessage}`}
      />
    );
  }
}