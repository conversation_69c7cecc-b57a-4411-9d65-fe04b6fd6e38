'use server';

import { revalidatePath } from 'next/cache';
import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

// Interface for order data
interface Order {
  id: string;
  displayId: string;
  status: string;
  email: string;
  [key: string]: unknown;
}

/**
 * Get list of orders
 */
export async function getOrders(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id
    displayId
    status
    email
    taxRate
    canceledAt
    createdAt
    updatedAt
    user {
      id
      name
      email
    }
    shippingAddress {
      id
      company
      firstName
      lastName
      address1
      address2
      city
      province
      postalCode
      phone
    }
    billingAddress {
      id
      company
      firstName
      lastName
      address1
      address2
      city
      province
      postalCode
      phone
    }
    lineItems {
      id
      title
      quantity
      sku
      thumbnail
      formattedUnitPrice
      formattedTotal
      variantData
      productData
    }
    total
  `
) {
  const query = `
    query GetOrders($where: OrderWhereInput, $take: Int!, $skip: Int!, $orderBy: [OrderOrderByInput!]) {
      items: orders(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: ordersCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, { where, take, skip, orderBy });
  return response;
}

/**
 * Get a single order by ID with full details for order pages
 */
export async function getOrder(orderId: string) {
  const query = `
    query ($id: ID!) {
      order(where: { id: $id }) {
        id
        displayId
        status
        fulfillmentStatus
        fulfillmentDetails
        total
        subtotal
        shipping
        tax
        note
        currency {
          code
          symbol
          name
        }
        metadata
        email
        createdAt
        updatedAt
        canceledAt
        paymentDetails
        totalPaid
        formattedTotalPaid
        lineItems {
          id
          quantity
          title
          sku
          thumbnail
          metadata
          variantTitle
          formattedUnitPrice
          formattedTotal
          productData
          variantData
        }
        fulfillments {
          id
          createdAt
          canceledAt
          fulfillmentItems {
            id
            quantity
            lineItem {
              id
              quantity
              title
              sku
              thumbnail
              metadata
              variantTitle
              formattedUnitPrice
              formattedTotal
              productData
              variantData
            }
          }
          shippingLabels {
            id
            labelUrl
            trackingNumber
            trackingUrl
            carrier
          }
        }
        unfulfilled
        user {
          id
          name
          email
          phone
          orders {
            id
          }
        }
        billingAddress {
          id
          firstName
          lastName
          company
          address1
          address2
          city
          province
          postalCode
          phone
          country {
            name
          }
        }
        shippingAddress {
          id
          firstName
          lastName
          company
          address1
          address2
          city
          province
          postalCode
          phone
          country {
            name
          }
        }
        events {
          id
          type
          data
          time
          createdAt
          user {
            id
            name
            email
          }
        }
      }
    }
  `;

  const cacheOptions = {
    next: {
      tags: [`order-${orderId}`],
      revalidate: 3600, // Cache for 1 hour
    },
  };

  const response = await keystoneClient(query, { id: orderId }, cacheOptions);
  return response;
}

/**
 * Update order status
 */
export async function updateOrderStatus(id: string, status: string) {
  const query = `
    mutation UpdateOrderStatus($id: ID!, $data: OrderUpdateInput!) {
      updateOrder(where: { id: $id }, data: $data) {
        id
        status
      }
    }
  `;

  const response = await keystoneClient(query, {
    id,
    data: { status }
  });

  if (response.success) {
    revalidatePath(`/dashboard/platform/orders/${id}`);
  } else {
    console.error(`Failed to update order status for ${id}:`, response.error);
  }

  return response;
}

/**
 * Get filtered orders based on status and search parameters
 */
export async function getFilteredOrders(
  status: string | null = null,
  search: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add status filter if provided and not 'all'
  if (status && status !== 'all') {
    where.status = { equals: status };
  }

  // Add search filter if provided
  if (search) {
    where.OR = [
      { displayId: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { user: { name: { contains: search, mode: 'insensitive' } } },
      { user: { email: { contains: search, mode: 'insensitive' } } },
    ];
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getOrders(where, pageSize, skip, orderBy);
}

/**
 * Get order counts by status
 */
export async function getOrderStatusCounts() {
  const query = `
    query GetOrderStatusCounts {
      pending: ordersCount(where: { status: { equals: pending } })
      requires_action: ordersCount(where: { status: { equals: requires_action } })
      completed: ordersCount(where: { status: { equals: completed } })
      archived: ordersCount(where: { status: { equals: archived } })
      canceled: ordersCount(where: { status: { equals: canceled } })
      all: ordersCount
    }
  `;

  const response = await keystoneClient(query);
  return response;
}