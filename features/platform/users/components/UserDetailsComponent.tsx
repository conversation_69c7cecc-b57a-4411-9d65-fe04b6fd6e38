"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, User as UserIcon, Mail, Phone, MapPin, Package, Users, Shield } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import type { User, Order, Address } from "../lib/types";

interface UserDetailsComponentProps {
  user: User;
}

export function UserDetailsComponent({ user }: UserDetailsComponentProps) {
  const [isOpen, setIsOpen] = useState(false);

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number | null | undefined, currencyCode?: string | null) => {
    if (!amount) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode || "USD",
    }).format(amount / 100);
  };

  const getOrderStatusBadgeVariant = (status: string | null | undefined): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "completed":
        return "default";
      case "pending":
        return "secondary";
      case "cancelled":
      case "refunded":
        return "destructive";
      default:
        return "outline";
    }
  };

  const fullName = [user.firstName, user.lastName].filter(Boolean).join(" ") || user.name || "Unnamed User";

  return (
    <div className="px-4 py-4 hover:bg-muted/50 transition-colors">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              <UserIcon className="h-5 w-5 text-primary" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium">{fullName}</h3>
                {user.hasAccount && (
                  <Badge variant="secondary" className="text-xs">
                    Account
                  </Badge>
                )}
                {user.role && (
                  <Badge variant="outline" className="text-xs">
                    {user.role.name}
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                {user.email && (
                  <span className="flex items-center gap-1">
                    <Mail className="h-3 w-3" />
                    {user.email}
                  </span>
                )}
                {user.phone && (
                  <span className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {user.phone}
                  </span>
                )}
                {user.team && (
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {user.team.name}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right mr-4">
              <div className="text-sm font-medium">{user.ordersCount || 0} orders</div>
              <div className="text-xs text-muted-foreground">
                {user.addressesCount || 0} addresses
              </div>
            </div>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-9 p-0">
                {isOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
                <span className="sr-only">Toggle details</span>
              </Button>
            </CollapsibleTrigger>
          </div>
        </div>
        <CollapsibleContent className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Orders Section */}
            <Card className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Recent Orders
                </h4>
                <Badge variant="secondary">{user.ordersCount || 0}</Badge>
              </div>
              <ScrollArea className="h-[200px]">
                {user.orders && user.orders.length > 0 ? (
                  <div className="space-y-2">
                    {user.orders.map((order: Order) => (
                      <div
                        key={order.id}
                        className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-sm">
                              #{order.displayId || order.id.slice(-8)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatDate(order.createdAt)}
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge variant={getOrderStatusBadgeVariant(order.status)}>
                              {order.status || "pending"}
                            </Badge>
                            <div className="text-sm font-medium mt-1">
                              {formatCurrency(order.total, order.currency?.code)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-sm text-muted-foreground py-8">
                    No orders yet
                  </div>
                )}
              </ScrollArea>
            </Card>

            {/* Addresses Section */}
            <Card className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Addresses
                </h4>
                <Badge variant="secondary">{user.addressesCount || 0}</Badge>
              </div>
              <ScrollArea className="h-[200px]">
                {user.addresses && user.addresses.length > 0 ? (
                  <div className="space-y-2">
                    {user.addresses.map((address: Address) => (
                      <div
                        key={address.id}
                        className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                      >
                        {address.label && (
                          <div className="font-medium text-sm mb-1">{address.label}</div>
                        )}
                        <div className="text-xs text-muted-foreground space-y-1">
                          {(address.firstName || address.lastName) && (
                            <div>{[address.firstName, address.lastName].filter(Boolean).join(" ")}</div>
                          )}
                          {address.address1 && <div>{address.address1}</div>}
                          {address.address2 && <div>{address.address2}</div>}
                          {(address.city || address.province || address.postalCode) && (
                            <div>
                              {[address.city, address.province, address.postalCode]
                                .filter(Boolean)
                                .join(", ")}
                            </div>
                          )}
                          {address.country && <div>{address.country.displayName}</div>}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-sm text-muted-foreground py-8">
                    No addresses yet
                  </div>
                )}
              </ScrollArea>
            </Card>
          </div>

          {/* Additional Info */}
          <div className="mt-4 pt-4 border-t flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              {user.role && (
                <div className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  <span>
                    Permissions:{" "}
                    {[
                      user.role.canManageProducts && "Products",
                      user.role.canManageOrders && "Orders",
                      user.role.canManageUsers && "Users",
                      user.role.canManageSettings && "Settings",
                    ]
                      .filter(Boolean)
                      .join(", ") || "None"}
                  </span>
                </div>
              )}
            </div>
            <div>
              Created: {formatDate(user.createdAt)}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}