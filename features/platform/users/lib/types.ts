export interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  phone?: string | null;
  hasAccount?: boolean | null;
  ordersCount?: number;
  role?: {
    id: string;
    name: string;
    canManageProducts: boolean;
    canManageOrders: boolean;
    canManageUsers: boolean;
    canManageSettings: boolean;
  } | null;
  orders?: Order[];
  createdAt?: string | null;
  updatedAt?: string | null;
  onboardingStatus?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  addresses?: Address[];
  addressesCount?: number;
  team?: {
    id: string;
    name: string;
  } | null;
}

export interface Order {
  id: string;
  displayId?: string | null;
  status?: string | null;
  total?: number | null;
  createdAt?: string | null;
  email?: string | null;
  currency?: {
    code: string;
  } | null;
}

export interface Address {
  id: string;
  label?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  address1?: string | null;
  address2?: string | null;
  city?: string | null;
  province?: string | null;
  postalCode?: string | null;
  country?: {
    displayName: string;
  } | null;
}

export interface UserStatusCounts {
  all: number;
  withAccount: number;
  withoutAccount: number;
}

export interface UserUpdateInput {
  name?: string | null;
  email?: string | null;
  phone?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  onboardingStatus?: string | null;
  role?: {
    connect?: { id: string };
    disconnect?: boolean;
  };
  team?: {
    connect?: { id: string };
    disconnect?: boolean;
  };
}

export interface UserCreateInput {
  name?: string | null;
  email?: string | null;
  phone?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  onboardingStatus?: string | null;
  password?: string;
  role?: {
    connect?: { id: string };
  };
  team?: {
    connect?: { id: string };
  };
}

// Sorting option interface
export interface SortOption {
  field: string;
  direction: 'ASC' | 'DESC';
}

// Response interfaces
export interface ApiListResponse<T> {
  items: T[];
  count: number;
}

export interface MutationResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}