'use server';

import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

/**
 * Get list of users
 */
export async function getUsers(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id
    name
    email
    phone
    hasAccount
    createdAt
    updatedAt
    onboardingStatus
    firstName
    lastName
    ordersCount
    addressesCount
    role {
      id
      name
    }
    team {
      id
      name
    }
    orders(take: 10, orderBy: { createdAt: desc }) {
      id
      displayId
      status
      total
      createdAt
      email
    }
    addresses(take: 5) {
      id
      label
      firstName
      lastName
      address1
      address2
      city
      province
      postalCode
    }
  `
) {
  const query = `
    query GetUsers($where: UserWhereInput, $take: Int!, $skip: Int!, $orderBy: [UserOrderByInput!]) {
      items: users(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: usersCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, { where, take, skip, orderBy });
  return response;
}

export async function getFilteredUsers(
  status: string | null = null,
  search: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add status filter if provided and not 'all'
  if (status && status !== 'all') {
    if (status === 'withAccount') {
      where.hasAccount = { equals: true };
    } else if (status === 'withoutAccount') {
      where.hasAccount = { equals: false };
    }
  }

  // Add search filter if provided
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getUsers(where, pageSize, skip, orderBy);
}

/**
 * Get user counts by status
 */
export async function getUserStatusCounts() {
  const query = `
    query GetUserStatusCounts {
      withAccount: usersCount(where: { hasAccount: { equals: true } })
      withoutAccount: usersCount(where: { hasAccount: { equals: false } })
      all: usersCount
    }
  `;

  const response = await keystoneClient(query);
  return response;
}