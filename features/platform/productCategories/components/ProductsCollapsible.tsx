"use client";

import React, { useState } from "react";
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from "@/components/ui/collapsible";
import { ChevronsUpDown, ArrowLeft, ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";

interface Product {
  id: string;
  title: string;
  handle?: string;
  status: string;
  thumbnail?: string;
  productType?: {
    id: string;
    value: string;
  };
  productVariants?: Array<{
    id: string;
    title: string;
    sku?: string;
    inventoryQuantity?: number;
  }>;
}

interface ProductsCollapsibleProps {
  categoryId: string;
  title: string;
  defaultOpen?: boolean;
  totalItems: number;
  products: Product[];
}

const statusColors = {
  draft: "zinc",
  proposed: "blue",
  published: "cyan",
  rejected: "rose",
} as const;

// Custom pagination component for products (triggers at 3+ items)
const ProductPagination = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange,
}: {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}) => {
  const [currentPageInput, setCurrentPageInput] = useState(currentPage.toString());
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  if (totalItems <= 3) {
    return null;
  }

  const handlePageChange = (newPage: number) => {
    const page = Math.max(1, Math.min(totalPages, Number(newPage)));
    onPageChange(page);
    setCurrentPageInput(page.toString());
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (newValue === "" || /^\d+$/.test(newValue)) {
      setCurrentPageInput(newValue);
    }
  };

  const handleInputBlur = () => {
    if (currentPageInput === "") {
      setCurrentPageInput(currentPage.toString());
    } else {
      handlePageChange(Number(currentPageInput));
    }
  };

  const colorClasses = "bg-white border-zinc-200 text-zinc-500 hover:bg-zinc-100 hover:text-zinc-700 focus:ring-zinc-700 dark:bg-zinc-950 dark:border-zinc-900 dark:text-zinc-300 dark:hover:text-white dark:hover:bg-zinc-700 dark:focus:ring-zinc-500";

  return (
    <div className="h-7 shadow-sm flex items-center border rounded-sm overflow-hidden">
      <button
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`border-0 h-full flex border-r items-center gap-1.5 pr-1.5 pl-1.5 uppercase text-xs p-[.15rem] font-medium focus:z-10 focus:ring-2 disabled:opacity-50 disabled:pointer-events-none ${colorClasses}`}
      >
        <ArrowLeft className="w-4 h-4" />
      </button>
      <div className="bg-background text-nowrap flex items-center border-r-0 px-1 text-sm h-full">
        <input
          className="mx-1 bg-transparent border-0 focus:ring-0 text-center appearance-none text-zinc-600 dark:text-zinc-100"
          style={{
            width: `${Math.max(
              0.5,
              Math.max(currentPageInput.toString().length) * 0.75
            )}em`,
          }}
          type="text"
          value={currentPageInput}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleInputBlur();
            }
          }}
        />
        <span className="mr-1.5 text-zinc-500 dark:text-zinc-400">
          / {totalPages}
        </span>
      </div>
      <button
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`border-0 h-full flex border-l items-center gap-1.5 pr-1.5 pl-1.5 uppercase text-xs p-[.15rem] font-medium focus:z-10 focus:ring-2 disabled:opacity-50 disabled:pointer-events-none ${colorClasses}`}
      >
        <ArrowRight className="w-4 h-4" />
      </button>
    </div>
  );
};

export const ProductsCollapsible = ({
  categoryId,
  title,
  defaultOpen = true,
  totalItems,
  products,
}: ProductsCollapsibleProps) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 3;

  // Calculate pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedItems = products.slice(startIndex, endIndex);

  const triggerClassName =
    "flex items-center rounded-sm shadow-sm uppercase tracking-wide border max-w-fit gap-2 text-nowrap pl-2.5 pr-1 py-[3px] text-sm font-medium text-blue-500 bg-white border-blue-200 hover:bg-blue-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-blue-950 dark:border-blue-900 dark:text-blue-300 dark:hover:text-white dark:hover:bg-blue-700 dark:focus:ring-blue-500 dark:focus:text-white";

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="flex flex-col gap-2 py-3 px-5 bg-blue-50/30 dark:bg-indigo-900/10 border-b"
    >
      <div className="flex items-center gap-2">
        <CollapsibleTrigger asChild>
          <button type="button" className={triggerClassName}>
            {totalItems} {title}
            {totalItems !== 1 && "s"}
            <ChevronsUpDown className="h-4 w-4" />
          </button>
        </CollapsibleTrigger>
        {isOpen && totalItems > 3 && (
          <ProductPagination
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={setCurrentPage}
          />
        )}
      </div>
      <CollapsibleContent className="space-y-2">
        {isOpen && (
          <>
            {totalItems > 3 && (
              <div className="text-xs text-muted-foreground">
                Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} items
              </div>
            )}
            {paginatedItems.map((product) => {
              const variantCount = product.productVariants?.length || 0;
              const totalInventory = product.productVariants?.reduce(
                (sum, variant) => sum + (variant.inventoryQuantity || 0),
                0
              ) || 0;

              return (
                <div
                  key={product.id}
                  className="relative border p-3 sm:p-4 bg-background rounded-sm"
                >
                  <div className="absolute top-2 right-2 sm:top-3 sm:right-3 flex flex-col gap-1 items-end">
                    <Badge
                      color={statusColors[product.status as keyof typeof statusColors] || "zinc"}
                      className="text-[.6rem] py-0.5 px-2 tracking-wide font-medium rounded-sm"
                    >
                      {product.status.toUpperCase()}
                    </Badge>
                    {product.productType && (
                      <Badge variant="secondary" className="text-[.6rem] py-0.5 px-2">
                        {product.productType.value}
                      </Badge>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 pr-16 sm:pr-20">
                    {product.thumbnail && (
                      <div className="flex-shrink-0 self-start">
                        <Image
                          src={product.thumbnail}
                          alt={product.title}
                          width={48}
                          height={48}
                          className="size-12 rounded-lg object-cover"
                        />
                      </div>
                    )}

                    <div className="flex-grow space-y-1">
                      <Link
                        href={`/dashboard/platform/products/${product.id}`}
                        className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400 block leading-tight"
                      >
                        {product.title}
                      </Link>
                      
                      {product.handle && (
                        <div className="text-xs text-muted-foreground">
                          {product.handle}
                        </div>
                      )}
                      
                      <div className="text-xs text-muted-foreground">
                        {variantCount} variant{variantCount !== 1 ? "s" : ""} • {totalInventory} in stock
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </>
        )}
      </CollapsibleContent>
    </Collapsible>
  );
}; 