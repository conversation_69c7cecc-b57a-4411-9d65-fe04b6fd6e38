"use client";

import React, { useState } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreVertical, Package, Info } from "lucide-react";
import Link from "next/link";
import { EditItemDrawer } from "@/features/dashboard/components/EditItemDrawer";
import { ProductCategory } from "../lib/types";
import { ProductsCollapsible } from "./ProductsCollapsible";

const statusColors = {
  active: "emerald",
  inactive: "zinc",
} as const;

interface ProductCategoryDetailsComponentProps {
  category: ProductCategory & { 
    productsCount?: number; 
    categoryChildrenCount?: number;
    products?: Array<{
      id: string;
      title: string;
      handle?: string;
      status: string;
      thumbnail?: string;
      productType?: {
        id: string;
        value: string;
      };
      productVariants?: Array<{
        id: string;
        title: string;
        sku?: string;
        inventoryQuantity?: number;
      }>;
    }>;
  };
}

export function ProductCategoryDetailsComponent({
  category,
}: ProductCategoryDetailsComponentProps) {
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);

  const productsCount = category.productsCount || 0;
  const childrenCount = category.categoryChildrenCount || 0;
  const products = category.products || [];

  return (
    <>
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value={category.id} className="border-0">
          <div className="px-4 md:px-6 py-3 md:py-4 flex justify-between w-full border-b relative min-h-[80px]">
            <div className="flex items-start gap-4">
              {/* Category Info */}
              <div className="flex flex-col items-start text-left gap-2 sm:gap-1.5">
                <div className="flex flex-wrap items-center gap-2">
                  <Link
                    href={`/dashboard/platform/product-categories/${category.id}`}
                    className="font-medium text-base hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {category.title}
                  </Link>
                  <span>‧</span>

                  <span className="text-sm font-medium">
                    <span className="text-muted-foreground/75">
                      {new Date(category.createdAt).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </span>
                  </span>
                </div>

                {category.handle && (
                  <span className="text-muted-foreground text-xs">
                    {category.handle}
                  </span>
                )}
                <div className="flex flex-wrap items-center gap-1.5 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <Package className="size-3" />
                    <span className="font-medium">{productsCount}</span>
                    <span>product{productsCount !== 1 ? "s" : ""}</span>
                  </div>
                  {childrenCount > 0 && (
                    <>
                      <span>‧</span>
                      <div className="flex items-center gap-1.5">
                        <Info className="size-3" />
                        <span className="font-medium">{childrenCount}</span>
                        <span>sub-categor{childrenCount !== 1 ? "ies" : "y"}</span>
                      </div>
                    </>
                  )}
                  {category.parentCategory && (
                    <>
                      <span>‧</span>
                      <div className="flex items-center gap-1.5">
                        <Info className="size-3" />
                        <span>Parent: {category.parentCategory.title}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="flex flex-col justify-between h-full">
              <div className="flex items-center gap-2">
                <Badge
                  color={
                    statusColors[category.isActive ? "active" : "inactive"]
                  }
                  className="text-[.6rem] sm:text-[.7rem] py-0 px-2 sm:px-3 tracking-wide font-medium rounded-md border h-6"
                >
                  {category.isActive ? "ACTIVE" : "INACTIVE"}
                </Badge>
                {/* Single buttons container */}
                <div className="absolute bottom-3 right-5 sm:static flex items-center gap-2">
                  <Button
                    variant="secondary"
                    size="icon"
                    className="border [&_svg]:size-3 h-6 w-6"
                    onClick={() => setIsEditDrawerOpen(true)}
                  >
                    <MoreVertical className="stroke-muted-foreground" />
                  </Button>
                  
                  {/* Category Details Popover */}
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="border [&_svg]:size-3 h-6 w-6"
                      >
                        <Info className="stroke-muted-foreground" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80" align="end">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Category Details</h4>
                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="text-muted-foreground">Handle:</span>
                              <div className="font-medium">
                                {category.handle || "Not set"}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Status:</span>
                              <div className="font-medium">
                                {category.isActive ? "Active" : "Inactive"}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Products:</span>
                              <div className="font-medium">
                                {productsCount}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Sub-categories:</span>
                              <div className="font-medium">
                                {childrenCount}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Parent Category Section */}
                        {category.parentCategory && (
                          <div className="space-y-2 pt-2 border-t">
                            <h4 className="font-medium text-sm">Parent Category</h4>
                            <Badge variant="secondary">
                              {category.parentCategory.title}
                            </Badge>
                          </div>
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>

                  <Button
                    variant="secondary"
                    size="icon"
                    className="border [&_svg]:size-3 h-6 w-6"
                    asChild
                  >
                    <AccordionTrigger className="py-0" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <AccordionContent className="pb-0">
            <div className="divide-y">
              <ProductsCollapsible
                categoryId={category.id}
                title="Product"
                totalItems={productsCount}
                products={products}
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <EditItemDrawer
        listKey="ProductCategory"
        itemId={category.id}
        open={isEditDrawerOpen}
        onClose={() => setIsEditDrawerOpen(false)}
      />
    </>
  );
}