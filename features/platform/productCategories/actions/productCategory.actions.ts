"use server";

import { keystoneClient } from "../../../dashboard/lib/keystoneClient";
import {
  SortOption,
  ProductCategory,
  ProductCategoryStatusCounts,
  ProductSubset,
  ProductCategoryUpdateInput,
  ProductCategoryCreateInput,
  // FilteredProductCategoriesResponse, // Will be replaced by direct KeystoneResponse
  // ProductCategoryResponse,         // Will be replaced by direct KeystoneResponse
  // ProductCategoryStatusCountsResponse, // Will be replaced by direct KeystoneResponse
  ProductsForCategoryResponse, // For getProductsForCategory
  MutationResponse,            // For mutations
  ApiListResponse,             // For list data shape
} from "../lib/types";

// Define a generic Keystone response type if not available globally,
// mirroring what keystoneClient is assumed to return.
// This is for type annotation purposes. The actual keystoneClient might have its own exported type.
type KeystoneClientResponse<T> = 
  | { success: true; data: T; error?: never }
  | { success: false; data?: T; error: any };

export async function getFilteredProductCategories(
  status: boolean | null,
  search: string | null,
  page: number,
  pageSize: number,
  sort: SortOption | null
): Promise<KeystoneClientResponse<ApiListResponse<ProductCategory>>> {
  const skip = (page - 1) * pageSize;
  const take = pageSize;

  let whereClauses: any[] = [];
  if (status !== null) {
    whereClauses.push({ isActive: { equals: status } });
  }
  if (search) {
    whereClauses.push({
      OR: [
        { title: { contains: search, mode: 'insensitive' } },
        { handle: { contains: search, mode: 'insensitive' } },
      ],
    });
  }

  const where = whereClauses.length > 0 ? { AND: whereClauses } : {};
  
  const orderBy = sort ? { [sort.field]: sort.direction.toLowerCase() } : { createdAt: 'desc' };

  const query = `
    query GetFilteredProductCategories(
      $where: ProductCategoryWhereInput
      $take: Int
      $skip: Int
      $orderBy: [ProductCategoryOrderByInput!]
    ) {
      items: productCategories(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        id
        title
        handle
        isActive
        isInternal
        createdAt
        updatedAt
        parentCategory {
          id
          title
        }
        categoryChildrenCount
        productsCount
        products(take: 20, orderBy: { title: asc }) {
          id
          title
          handle
          status
          thumbnail
          productType {
            id
            value
          }
          productVariants {
            id
            title
            sku
            inventoryQuantity
          }
        }
      }
      count: productCategoriesCount(where: $where)
    }
  `;

  const variables = { where, take, skip, orderBy: [orderBy] };
  
  // Return keystoneClient response directly
  return keystoneClient<ApiListResponse<ProductCategory>>(query, variables);
}

export async function getProductCategoryStatusCounts(): Promise<KeystoneClientResponse<ProductCategoryStatusCounts>> {
  const query = `
    query GetProductCategoryStatusCounts {
      all: productCategoriesCount
      active: productCategoriesCount(where: { isActive: { equals: true } })
      inactive: productCategoriesCount(where: { isActive: { equals: false } })
    }
  `;

  // Return keystoneClient response directly
  return keystoneClient<ProductCategoryStatusCounts>(query);
}

export async function getProductCategory(id: string): Promise<KeystoneClientResponse<{ productCategory: ProductCategory | null }>> {
  const query = `
    query GetProductCategory($id: ID!) {
      productCategory(where: { id: $id }) {
        id
        title
        handle
        metadata
        isInternal
        isActive
        createdAt
        updatedAt
        parentCategory {
          id
          title
          handle
        }
        categoryChildren {
          id
          title
          handle
        }
        products(take: 10, orderBy: { title: asc }) { 
          id
          title
          handle
          status
          thumbnail
        }
      }
    }
  `;
  // Return keystoneClient response directly
  return keystoneClient<{ productCategory: ProductCategory | null }>(query, { id });
}

// Other functions remain with their existing structure for now,
// as the primary task was to refactor the three main "get" functions.

export async function getProductsForCategory(
  categoryId: string,
  page: number,
  pageSize: number,
  search: string | null,
  sort: SortOption | null
): Promise<ProductsForCategoryResponse> {
  const skip = (page - 1) * pageSize;
  const take = pageSize;
  
  let productWhereClauses: any[] = [{ productCategories: { some: { id: { equals: categoryId } } } }];
  if (search) {
    productWhereClauses.push({
      OR: [
        { title: { contains: search, mode: 'insensitive' } },
        { handle: { contains: search, mode: 'insensitive' } },
      ],
    });
  }
  const where = { AND: productWhereClauses };
  const orderBy = sort ? { [sort.field]: sort.direction.toLowerCase() } : { title: 'asc' };

  const query = `
    query GetProductsForCategory(
      $where: ProductWhereInput
      $take: Int
      $skip: Int
      $orderBy: [ProductOrderByInput!]
    ) {
      items: products(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        id
        title
        handle
        status
        thumbnail
      }
      count: productsCount(where: $where)
    }
  `;
  const variables = { where, take, skip, orderBy: [orderBy] };

  try {
    const response = await keystoneClient<ApiListResponse<ProductSubset>>(query, variables);
    if (!response.success || !response.data) {
      return { success: false, error: response.error || "Failed to fetch products for category." };
    }
    return { success: true, data: response.data };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred.";
    console.error(`Error in getProductsForCategory for categoryId ${categoryId}:`, message);
    return { success: false, error: message };
  }
}

export async function updateProductCategory(
  id: string,
  input: ProductCategoryUpdateInput
): Promise<MutationResponse<ProductCategory>> {
  const query = `
    mutation UpdateProductCategory($id: ID!, $data: ProductCategoryUpdateInput!) {
      item: updateProductCategory(where: { id: $id }, data: $data) {
        id
        title
        handle
        metadata
        isInternal
        isActive
        createdAt
        updatedAt
        parentCategory { id title handle }
      }
    }
  `;
  try {
    const response = await keystoneClient<{ item: ProductCategory | null }>(query, { id, data: input });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Product category not found or update failed." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during update.";
    console.error(`Error in updateProductCategory for id ${id}:`, message);
    return { success: false, error: message };
  }
}

export async function deleteProductCategory(id: string): Promise<MutationResponse<{ id: string }>> {
  const query = `
    mutation DeleteProductCategory($id: ID!) {
      item: deleteProductCategory(where: { id: $id }) {
        id
      }
    }
  `;
  try {
    const response = await keystoneClient<{ item: { id: string } | null }>(query, { id });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Product category not found or delete failed." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during delete.";
    console.error(`Error in deleteProductCategory for id ${id}:`, message);
    return { success: false, error: message };
  }
}

export async function createProductCategory(
  input: ProductCategoryCreateInput
): Promise<MutationResponse<ProductCategory>> {
  const query = `
    mutation CreateProductCategory($data: ProductCategoryCreateInput!) {
      item: createProductCategory(data: $data) {
        id
        title
        handle
        metadata
        isInternal
        isActive
        createdAt
        updatedAt
        parentCategory { id title handle }
      }
    }
  `;
   try {
    const response = await keystoneClient<{ item: ProductCategory }>(query, { data: input });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Failed to create product category." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during creation.";
    console.error("Error in createProductCategory:", message);
    return { success: false, error: message };
  }
}