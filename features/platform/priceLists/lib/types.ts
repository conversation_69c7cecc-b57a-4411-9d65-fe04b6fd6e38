export interface PriceList {
  id: string;
  name?: string | null;
  description?: string | null;
  type?: 'sale' | 'override' | null;
  status?: 'active' | 'draft' | null;
  startsAt?: string | null;
  endsAt?: string | null;
  moneyAmounts?: MoneyAmount[];
  moneyAmountsCount?: number;
  customerGroups?: CustomerGroup[];
  customerGroupsCount?: number;
  createdAt?: string | null;
  updatedAt?: string | null;
}

export interface MoneyAmount {
  id: string;
  amount?: number | null;
  compareAmount?: number | null;
  minQuantity?: number | null;
  maxQuantity?: number | null;
  productVariant?: {
    id: string;
    title?: string | null;
    sku?: string | null;
    product?: {
      id: string;
      title?: string | null;
      thumbnail?: string | null;
    } | null;
  } | null;
  region?: {
    id: string;
    name: string;
    currencyCode: string;
  } | null;
  currency?: {
    id: string;
    code: string;
    symbol: string;
  } | null;
  displayPrice?: string | null;
  calculatedPrice?: {
    calculatedAmount?: number | null;
    originalAmount?: number | null;
    currencyCode?: string | null;
  } | null;
}

export interface CustomerGroup {
  id: string;
  name: string;
  metadata?: any | null;
}

export interface PriceListStatusCounts {
  all: number;
  active: number;
  draft: number;
  scheduled: number;
  expired: number;
}

export interface PriceListUpdateInput {
  name?: string | null;
  description?: string | null;
  type?: 'sale' | 'override' | null;
  status?: 'active' | 'draft' | null;
  startsAt?: string | null;
  endsAt?: string | null;
  customerGroups?: {
    connect?: Array<{ id: string }>;
    disconnect?: Array<{ id: string }>;
  };
}

export interface PriceListCreateInput {
  name: string;
  description?: string | null;
  type?: 'sale' | 'override' | null;
  status?: 'active' | 'draft' | null;
  startsAt?: string | null;
  endsAt?: string | null;
  customerGroups?: {
    connect?: Array<{ id: string }>;
  };
}

// Sorting option interface
export interface SortOption {
  field: string;
  direction: 'ASC' | 'DESC';
}

// Response interfaces
export interface ApiListResponse<T> {
  items: T[];
  count: number;
}

export interface MutationResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}