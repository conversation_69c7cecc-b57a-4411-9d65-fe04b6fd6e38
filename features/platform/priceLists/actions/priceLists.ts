"use server";

import { keystoneClient } from "../../../dashboard/lib/keystoneClient";
import {
  SortOption,
  PriceList,
  PriceListStatusCounts,
  PriceListUpdateInput,
  PriceListCreateInput,
  MutationResponse,
  ApiListResponse,
} from "../lib/types";

type KeystoneClientResponse<T> = 
  | { success: true; data: T; error?: never }
  | { success: false; data?: T; error: any };

export async function getFilteredPriceLists(
  status: 'active' | 'draft' | 'scheduled' | 'expired' | null,
  search: string | null,
  page: number,
  pageSize: number,
  sort: SortOption | null
): Promise<KeystoneClientResponse<ApiListResponse<PriceList>>> {
  const skip = (page - 1) * pageSize;
  const take = pageSize;

  let whereClauses: any[] = [];
  
  const now = new Date().toISOString();
  
  if (status === 'active') {
    whereClauses.push({
      AND: [
        { status: { equals: 'active' } },
        {
          OR: [
            { startsAt: { lte: now } },
            { startsAt: { equals: null } }
          ]
        },
        {
          OR: [
            { endsAt: { gte: now } },
            { endsAt: { equals: null } }
          ]
        }
      ]
    });
  } else if (status === 'draft') {
    whereClauses.push({ status: { equals: 'draft' } });
  } else if (status === 'scheduled') {
    whereClauses.push({
      AND: [
        { status: { equals: 'active' } },
        { startsAt: { gt: now } }
      ]
    });
  } else if (status === 'expired') {
    whereClauses.push({
      AND: [
        { endsAt: { not: { equals: null } } },
        { endsAt: { lt: now } }
      ]
    });
  }
  
  if (search) {
    whereClauses.push({
      OR: [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ],
    });
  }

  const where = whereClauses.length > 0 ? { AND: whereClauses } : {};
  
  const orderBy = sort ? { [sort.field]: sort.direction.toLowerCase() } : { createdAt: 'desc' };

  const query = `
    query GetFilteredPriceLists(
      $where: PriceListWhereInput
      $take: Int
      $skip: Int
      $orderBy: [PriceListOrderByInput!]
    ) {
      items: priceLists(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        id
        name
        description
        type
        status
        startsAt
        endsAt
        createdAt
        updatedAt
        moneyAmountsCount
        customerGroupsCount
        customerGroups(take: 5) {
          id
          name
          metadata
        }
        moneyAmounts(take: 10, orderBy: { createdAt: desc }) {
          id
          amount
          compareAmount
          minQuantity
          maxQuantity
          displayPrice
          productVariant {
            id
            title
            sku
            product {
              id
              title
              thumbnail
            }
          }
          region {
            id
            name
            currencyCode
          }
          currency {
            id
            code
            symbol
          }
          calculatedPrice {
            calculatedAmount
            originalAmount
            currencyCode
          }
        }
      }
      count: priceListsCount(where: $where)
    }
  `;

  const variables = { where, take, skip, orderBy: [orderBy] };
  
  return keystoneClient<ApiListResponse<PriceList>>(query, variables);
}

export async function getPriceListStatusCounts(): Promise<KeystoneClientResponse<PriceListStatusCounts>> {
  const now = new Date().toISOString();
  
  const query = `
    query GetPriceListStatusCounts($now: DateTime!) {
      all: priceListsCount
      draft: priceListsCount(where: { status: { equals: draft } })
      active: priceListsCount(where: {
        AND: [
          { status: { equals: active } },
          {
            OR: [
              { startsAt: { lte: $now } },
              { startsAt: { equals: null } }
            ]
          },
          {
            OR: [
              { endsAt: { gte: $now } },
              { endsAt: { equals: null } }
            ]
          }
        ]
      })
      scheduled: priceListsCount(where: {
        AND: [
          { status: { equals: active } },
          { startsAt: { gt: $now } }
        ]
      })
      expired: priceListsCount(where: {
        AND: [
          { endsAt: { not: { equals: null } } },
          { endsAt: { lt: $now } }
        ]
      })
    }
  `;

  return keystoneClient<PriceListStatusCounts>(query, { now });
}

export async function getPriceList(id: string): Promise<KeystoneClientResponse<{ priceList: PriceList | null }>> {
  const query = `
    query GetPriceList($id: ID!) {
      priceList(where: { id: $id }) {
        id
        name
        description
        type
        status
        startsAt
        endsAt
        createdAt
        updatedAt
        moneyAmountsCount
        customerGroupsCount
        customerGroups {
          id
          name
          metadata
        }
        moneyAmounts(orderBy: { createdAt: desc }) {
          id
          amount
          compareAmount
          minQuantity
          maxQuantity
          displayPrice
          productVariant {
            id
            title
            sku
            product {
              id
              title
              thumbnail
            }
          }
          region {
            id
            name
            currencyCode
          }
          currency {
            id
            code
            symbol
          }
          calculatedPrice {
            calculatedAmount
            originalAmount
            currencyCode
          }
        }
      }
    }
  `;
  
  return keystoneClient<{ priceList: PriceList | null }>(query, { id });
}

export async function updatePriceList(
  id: string,
  input: PriceListUpdateInput
): Promise<MutationResponse<PriceList>> {
  const query = `
    mutation UpdatePriceList($id: ID!, $data: PriceListUpdateInput!) {
      item: updatePriceList(where: { id: $id }, data: $data) {
        id
        name
        description
        type
        status
        startsAt
        endsAt
        createdAt
        updatedAt
        customerGroups {
          id
          name
        }
      }
    }
  `;
  try {
    const response = await keystoneClient<{ item: PriceList | null }>(query, { id, data: input });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Price list not found or update failed." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during update.";
    console.error(`Error in updatePriceList for id ${id}:`, message);
    return { success: false, error: message };
  }
}

export async function deletePriceList(id: string): Promise<MutationResponse<{ id: string }>> {
  const query = `
    mutation DeletePriceList($id: ID!) {
      item: deletePriceList(where: { id: $id }) {
        id
      }
    }
  `;
  try {
    const response = await keystoneClient<{ item: { id: string } | null }>(query, { id });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Price list not found or delete failed." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during delete.";
    console.error(`Error in deletePriceList for id ${id}:`, message);
    return { success: false, error: message };
  }
}

export async function createPriceList(
  input: PriceListCreateInput
): Promise<MutationResponse<PriceList>> {
  const query = `
    mutation CreatePriceList($data: PriceListCreateInput!) {
      item: createPriceList(data: $data) {
        id
        name
        description
        type
        status
        startsAt
        endsAt
        createdAt
        updatedAt
        customerGroups {
          id
          name
        }
      }
    }
  `;
   try {
    const response = await keystoneClient<{ item: PriceList }>(query, { data: input });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Failed to create price list." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during creation.";
    console.error("Error in createPriceList:", message);
    return { success: false, error: message };
  }
}