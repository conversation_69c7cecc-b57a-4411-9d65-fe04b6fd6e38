"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, DollarSign, Tag, Users, Calendar, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import type { PriceList, MoneyAmount } from "../lib/types";

interface PriceListDetailsComponentProps {
  priceList: PriceList;
}

export function PriceListDetailsComponent({ priceList }: PriceListDetailsComponentProps) {
  const [isOpen, setIsOpen] = useState(false);

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  const formatCurrency = (amount: number | null | undefined, currencyCode?: string | null) => {
    if (amount === null || amount === undefined) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode || "USD",
    }).format(amount / 100);
  };

  const getPriceListStatus = () => {
    const now = new Date();
    const startsAt = priceList.startsAt ? new Date(priceList.startsAt) : null;
    const endsAt = priceList.endsAt ? new Date(priceList.endsAt) : null;

    if (priceList.status === 'draft') return 'draft';
    if (endsAt && endsAt < now) return 'expired';
    if (startsAt && startsAt > now) return 'scheduled';
    if (priceList.status === 'active') return 'active';
    return 'unknown';
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "active":
        return "default";
      case "draft":
      case "scheduled":
        return "secondary";
      case "expired":
        return "destructive";
      default:
        return "outline";
    }
  };

  const getTypeBadgeVariant = (type: string | null | undefined): "default" | "secondary" => {
    return type === 'sale' ? "default" : "secondary";
  };

  const status = getPriceListStatus();

  const getDiscountPercentage = (amount: MoneyAmount) => {
    if (!amount.compareAmount || !amount.amount) return null;
    const discount = ((amount.compareAmount - amount.amount) / amount.compareAmount) * 100;
    return discount.toFixed(0);
  };

  return (
    <div className="px-4 py-4 hover:bg-muted/50 transition-colors">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              <Tag className="h-5 w-5 text-primary" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium">{priceList.name || "Unnamed Price List"}</h3>
                <Badge variant={getStatusBadgeVariant(status)}>
                  {status}
                </Badge>
                {priceList.type && (
                  <Badge variant={getTypeBadgeVariant(priceList.type)}>
                    {priceList.type}
                  </Badge>
                )}
              </div>
              <div className="text-sm text-muted-foreground">
                {priceList.description || "No description"}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right mr-4">
              <div className="text-sm font-medium">
                {priceList.moneyAmountsCount || 0} prices
              </div>
              <div className="text-xs text-muted-foreground">
                {priceList.customerGroupsCount || 0} groups
              </div>
            </div>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-9 p-0">
                {isOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
                <span className="sr-only">Toggle details</span>
              </Button>
            </CollapsibleTrigger>
          </div>
        </div>
        <CollapsibleContent className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Price Overrides Section */}
            <Card className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Price Overrides
                </h4>
                <Badge variant="secondary">{priceList.moneyAmountsCount || 0}</Badge>
              </div>
              <ScrollArea className="h-[200px]">
                {priceList.moneyAmounts && priceList.moneyAmounts.length > 0 ? (
                  <div className="space-y-2">
                    {priceList.moneyAmounts.map((amount: MoneyAmount) => (
                      <div
                        key={amount.id}
                        className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                      >
                        {amount.productVariant && (
                          <div className="flex items-start gap-3">
                            {amount.productVariant.product?.thumbnail && (
                              <img
                                src={amount.productVariant.product.thumbnail}
                                alt={amount.productVariant.product.title || "Product"}
                                className="w-10 h-10 rounded object-cover"
                              />
                            )}
                            <div className="flex-1">
                              <div className="font-medium text-sm">
                                {amount.productVariant.product?.title || "Unknown Product"}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {amount.productVariant.title} • {amount.productVariant.sku}
                              </div>
                              <div className="flex items-center gap-2 mt-1">
                                <span className="text-sm font-medium">
                                  {formatCurrency(amount.amount, amount.currency?.code || amount.region?.currencyCode)}
                                </span>
                                {amount.compareAmount && amount.compareAmount > (amount.amount || 0) && (
                                  <>
                                    <span className="text-xs text-muted-foreground line-through">
                                      {formatCurrency(amount.compareAmount, amount.currency?.code || amount.region?.currencyCode)}
                                    </span>
                                    <Badge variant="secondary" className="text-xs">
                                      -{getDiscountPercentage(amount)}%
                                    </Badge>
                                  </>
                                )}
                              </div>
                              {(amount.minQuantity || amount.maxQuantity) && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  Qty: {amount.minQuantity || 1} - {amount.maxQuantity || "∞"}
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-sm text-muted-foreground py-8">
                    No price overrides yet
                  </div>
                )}
              </ScrollArea>
            </Card>

            {/* Details Section */}
            <Card className="p-4">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Details & Schedule
              </h4>
              <div className="space-y-3">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Type:</span>
                    <span className="capitalize">{priceList.type || "N/A"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status:</span>
                    <span className="capitalize">{priceList.status || "N/A"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Start Date:</span>
                    <span>{formatDateTime(priceList.startsAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">End Date:</span>
                    <span>{priceList.endsAt ? formatDateTime(priceList.endsAt) : "No expiry"}</span>
                  </div>
                </div>

                {/* Customer Groups */}
                {priceList.customerGroups && priceList.customerGroups.length > 0 && (
                  <div className="pt-3 border-t">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-3 w-3" />
                      <span className="text-sm font-medium">Customer Groups</span>
                    </div>
                    <div className="flex gap-1 flex-wrap">
                      {priceList.customerGroups.map((group) => (
                        <Badge key={group.id} variant="outline" className="text-xs">
                          {group.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Additional Info */}
          <div className="mt-4 pt-4 border-t flex items-center justify-between text-xs text-muted-foreground">
            <div>
              Created: {formatDate(priceList.createdAt)}
            </div>
            <div>
              Updated: {formatDate(priceList.updatedAt)}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}