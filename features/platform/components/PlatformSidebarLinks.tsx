import React from 'react';
import { platformNavItems } from '../lib/navigation';

interface PlatformSidebarLinksProps {
  basePath?: string;
}

export interface PlatformSidebarLink {
  title: string;
  href: string;
}

export function PlatformSidebarLinks({ basePath = '' }: PlatformSidebarLinksProps): PlatformSidebarLink[] {
  return platformNavItems.map(item => ({
    title: item.title,
    href: `${basePath}${item.href}`,
  }));
}

// Export a hook version as well for more flexibility
export function usePlatformSidebarLinks(basePath?: string): PlatformSidebarLink[] {
  return React.useMemo(() => PlatformSidebarLinks({ basePath }), [basePath]);
}