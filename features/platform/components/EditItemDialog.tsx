'use client';

import React, { useC<PERSON>back, useMemo, useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  <PERSON>er,
  Drawer<PERSON>ontent,
  DrawerHeader,
  DrawerTit<PERSON>,
  Drawer<PERSON>ody,
  DrawerFooter,
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Fields } from '@/features/dashboard/components/Fields';
import { useItemForm } from '@/features/dashboard/lib/useItemForm';
import { makeDataGetter } from '@/features/dashboard/lib/dataGetter';
import { toast } from 'sonner';
import { GraphQLErrorNotice } from '@/features/dashboard/components/GraphQLErrorNotice';
import { useList } from '@/features/dashboard/hooks/useAdminMeta';
import { getItemAction } from '@/features/dashboard/actions';
import { useIsMobile } from '@/components/ui/use-mobile';
import { Skeleton } from '@/components/ui/skeleton';
import type { ItemData } from '@/features/dashboard/lib/serialization';
import type { FieldMeta } from '@/features/dashboard/types';

interface EditItemDialogProps {
  listKey: string;
  itemId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSaved?: () => void;
  fields?: string[];
  title?: string;
}

export function EditItemDialog({
  listKey,
  itemId,
  open,
  onOpenChange,
  onSaved,
  fields,
  title,
}: EditItemDialogProps) {
  const { list } = useList(listKey);
  const [item, setItem] = useState<Record<string, unknown> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const isMobile = useIsMobile();

  // Fetch item data when dialog opens
  useEffect(() => {
    if (!open || !list) {
      return;
    }

    const fetchItem = async () => {
      setIsLoading(true);
      try {
        const response = await getItemAction(list, itemId);
        if (response.success && response.data?.item) {
          setItem(response.data.item as Record<string, unknown>);
        } else {
          toast.error('Failed to load item');
          onOpenChange(false);
        }
      } catch (err) {
        console.error('Error fetching item:', err);
        toast.error('Failed to load item');
        onOpenChange(false);
      } finally {
        setIsLoading(false);
      }
    };

    fetchItem();
  }, [open, list, itemId, onOpenChange]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setItem(null);
      setIsLoading(false);
    }
  }, [open]);

  if (!list || !open) {
    return null;
  }

  const dialogTitle = title || `Edit ${list.singular}`;

  // Desktop: Dialog
  if (!isMobile) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col gap-0 p-0">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle>{dialogTitle}</DialogTitle>
          </DialogHeader>
          <EditItemDialogContent
            list={list}
            item={item}
            itemId={itemId}
            isLoading={isLoading}
            onClose={() => onOpenChange(false)}
            onSaved={onSaved}
            fields={fields}
            isDesktop={true}
          />
        </DialogContent>
      </Dialog>
    );
  }

  // Mobile: Drawer
  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DrawerTitle>{dialogTitle}</DrawerTitle>
        </DrawerHeader>
        <EditItemDialogContent
          list={list}
          item={item}
          itemId={itemId}
          isLoading={isLoading}
          onClose={() => onOpenChange(false)}
          onSaved={onSaved}
          fields={fields}
          isDesktop={false}
        />
      </DrawerContent>
    </Drawer>
  );
}

// Loading skeleton
function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Text fields */}
      {[1, 2, 3].map((field) => (
        <div key={field} className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-10 w-full rounded-md" />
          {field === 2 && <Skeleton className="h-3 w-3/4 mt-1" />}
        </div>
      ))}

      {/* Select field */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-10 w-full rounded-md" />
      </div>

      {/* Textarea field */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-28" />
        <Skeleton className="h-24 w-full rounded-md" />
      </div>
    </div>
  );
}

// Separate component for the dialog content
function EditItemDialogContent({
  list,
  item,
  itemId,
  isLoading,
  onClose,
  onSaved,
  fields,
  isDesktop,
}: {
  list: any;
  item: Record<string, unknown> | null;
  itemId: string;
  isLoading: boolean;
  onClose: () => void;
  onSaved?: () => void;
  fields?: string[];
  isDesktop: boolean;
}) {
  // If still loading, show skeleton
  if (isLoading || !item) {
    if (isDesktop) {
      return (
        <>
          <div className="flex-1 overflow-y-auto px-6 py-6">
            <LoadingSkeleton />
          </div>
          <DialogFooter className="px-6 py-4 border-t">
            <Button variant="outline" disabled>Cancel</Button>
            <Button disabled>Save Changes</Button>
          </DialogFooter>
        </>
      );
    } else {
      return (
        <>
          <DrawerBody className="px-4 pt-2">
            <LoadingSkeleton />
          </DrawerBody>
          <DrawerFooter>
            <Button variant="outline" disabled>Cancel</Button>
            <Button disabled>Save Changes</Button>
          </DrawerFooter>
        </>
      );
    }
  }

  return (
    <EditItemForm
      list={list}
      item={item}
      itemId={itemId}
      onClose={onClose}
      onSaved={onSaved}
      fields={fields}
      isDesktop={isDesktop}
    />
  );
}

// Form component
function EditItemForm({
  list,
  item,
  onClose,
  onSaved,
  fields,
  isDesktop,
}: {
  list: any;
  item: Record<string, unknown>;
  onClose: () => void;
  onSaved?: () => void;
  fields?: string[];
  isDesktop: boolean;
}) {
  const itemWithId = item as unknown as ItemData;
  const dataGetter = makeDataGetter(itemWithId, undefined);

  // Filter fields if specified
  const fieldsToUse = useMemo(() => {
    if (!fields) return list.fields;
    return Object.fromEntries(
      Object.entries(list.fields).filter(([path]) => fields.includes(path))
    );
  }, [list.fields, fields]);

  // Get field modes and positions
  const { fieldModes, fieldPositions } = useMemo(() => {
    const modes: Record<string, 'edit' | 'read' | 'hidden'> = {};
    const positions: Record<string, 'form' | 'sidebar'> = {};

    Object.entries(fieldsToUse).forEach(([path, field]) => {
      const fieldMeta = field as FieldMeta;
      modes[path] = (fieldMeta.itemView?.fieldMode || 'edit') as any;
      positions[path] = (fieldMeta.itemView?.fieldPosition || 'form') as any;
    });

    return { fieldModes: modes, fieldPositions: positions };
  }, [fieldsToUse]);

  // Get selected fields for GraphQL
  const selectedFields = useMemo(() => {
    return Object.keys(list.fields)
      .map((key) => list.fields[key]?.controller?.graphqlSelection)
      .filter(Boolean)
      .join('\n');
  }, [list.fields]);

  // Use the form hook
  const {
    state,
    setValue,
    loading: formLoading,
    error: formError,
    forceValidation,
    invalidFields,
    changedFields,
    onSave,
    onReset,
  } = useItemForm({
    list,
    fields: fieldsToUse,
    selectedFields,
    itemGetter: dataGetter,
  });

  const handleValueChange = useCallback(
    (valueUpdater: any) => {
      setValue((state: any) => ({
        item: state.item,
        value: valueUpdater(state.value),
      }));
    },
    [setValue]
  );

  const handleSave = async () => {
    try {
      await onSave();
      onSaved?.();
      onClose();
    } catch (err) {
      // Error is already handled by useItemForm
    }
  };

  const Footer = () => (
    <>
      <Button variant="outline" onClick={onClose}>
        Cancel
      </Button>
      {changedFields.size > 0 && (
        <Button variant="outline" onClick={onReset} disabled={formLoading}>
          Reset
        </Button>
      )}
      <Button
        onClick={handleSave}
        disabled={!changedFields.size || formLoading}
      >
        {formLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        Save Changes
      </Button>
    </>
  );

  if (isDesktop) {
    return (
      <>
        <div className="flex-1 overflow-y-auto px-6 py-6">
          {formError && (
            <GraphQLErrorNotice
              networkError={(formError as any)?.networkError}
              errors={(formError as any)?.graphQLErrors}
            />
          )}

          <Fields
            fields={fieldsToUse}
            fieldModes={fieldModes}
            fieldPositions={fieldPositions}
            forceValidation={forceValidation}
            invalidFields={invalidFields}
            value={state.value as any}
            onChange={handleValueChange}
            position="form"
            groups={list.groups || []}
          />
        </div>
        <DialogFooter className="px-6 py-4 border-t">
          <Footer />
        </DialogFooter>
      </>
    );
  }

  // Mobile drawer
  return (
    <>
      <DrawerBody className="px-4 pt-2">
        {formError && (
          <GraphQLErrorNotice
            networkError={(formError as any)?.networkError}
            errors={(formError as any)?.graphQLErrors}
          />
        )}

        <Fields
          fields={fieldsToUse}
          fieldModes={fieldModes}
          fieldPositions={fieldPositions}
          forceValidation={forceValidation}
          invalidFields={invalidFields}
          value={state.value as any}
          onChange={handleValueChange}
          position="form"
          groups={list.groups || []}
        />
      </DrawerBody>
      <DrawerFooter>
        <Footer />
      </DrawerFooter>
    </>
  );
}

export default EditItemDialog;