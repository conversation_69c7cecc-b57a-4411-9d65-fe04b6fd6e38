import React from 'react';
import Link from 'next/link';
import { ArrowUpRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { platformNavItems } from '../lib/navigation';

interface PlatformNavCardsProps {
  basePath?: string;
}

// Map color classes to the new card color format with gradients
const getColorConfig = (colorClass: string) => {
  const colorMap: Record<
    string,
    {
      iconForeground: string;
      iconBackground: string;
      ringColorClass: string;
    }
  > = {
    blue: {
      iconForeground: 'text-blue-500 dark:text-blue-400',
      iconBackground:
        'bg-gradient-to-br from-blue-100 via-blue-50 to-white dark:from-blue-500/20 dark:via-blue-500/10 dark:to-blue-500/5',
      ringColorClass: 'ring-blue-500/20 dark:ring-blue-400/20',
    },
    green: {
      iconForeground: 'text-green-500 dark:text-green-400',
      iconBackground:
        'bg-gradient-to-br from-green-100 via-green-50 to-white dark:from-green-500/20 dark:via-green-500/10 dark:to-green-500/5',
      ringColorClass: 'ring-green-500/20 dark:ring-green-400/20',
    },
    purple: {
      iconForeground: 'text-purple-500 dark:text-purple-400',
      iconBackground:
        'bg-gradient-to-br from-purple-100 via-purple-50 to-white dark:from-purple-500/20 dark:via-purple-500/10 dark:to-purple-500/5',
      ringColorClass: 'ring-purple-500/20 dark:ring-purple-400/20',
    },
    pink: {
      iconForeground: 'text-pink-500 dark:text-pink-400',
      iconBackground:
        'bg-gradient-to-br from-pink-100 via-pink-50 to-white dark:from-pink-500/20 dark:via-pink-500/10 dark:to-pink-500/5',
      ringColorClass: 'ring-pink-500/20 dark:ring-pink-400/20',
    },
    orange: {
      iconForeground: 'text-orange-500 dark:text-orange-400',
      iconBackground:
        'bg-gradient-to-br from-orange-100 via-orange-50 to-white dark:from-orange-500/20 dark:via-orange-500/10 dark:to-orange-500/5',
      ringColorClass: 'ring-orange-500/20 dark:ring-orange-400/20',
    },
    emerald: {
      iconForeground: 'text-emerald-500 dark:text-emerald-400',
      iconBackground:
        'bg-gradient-to-br from-emerald-100 via-emerald-50 to-white dark:from-emerald-500/20 dark:via-emerald-500/10 dark:to-emerald-500/5',
      ringColorClass: 'ring-emerald-500/20 dark:ring-emerald-400/20',
    },
    gray: {
      iconForeground: 'text-gray-500 dark:text-gray-400',
      iconBackground:
        'bg-gradient-to-br from-gray-100 via-gray-50 to-white dark:from-gray-500/20 dark:via-gray-500/10 dark:to-gray-500/5',
      ringColorClass: 'ring-gray-500/20 dark:ring-gray-400/20',
    },
    indigo: {
      iconForeground: 'text-indigo-500 dark:text-indigo-400',
      iconBackground:
        'bg-gradient-to-br from-indigo-100 via-indigo-50 to-white dark:from-indigo-500/20 dark:via-indigo-500/10 dark:to-indigo-500/5',
      ringColorClass: 'ring-indigo-500/20 dark:ring-indigo-400/20',
    },
    cyan: {
      iconForeground: 'text-cyan-500 dark:text-cyan-400',
      iconBackground:
        'bg-gradient-to-br from-cyan-100 via-cyan-50 to-white dark:from-cyan-500/20 dark:via-cyan-500/10 dark:to-cyan-500/5',
      ringColorClass: 'ring-cyan-500/20 dark:ring-cyan-400/20',
    },
    red: {
      iconForeground: 'text-red-500 dark:text-red-400',
      iconBackground:
        'bg-gradient-to-br from-red-100 via-red-50 to-white dark:from-red-500/20 dark:via-red-500/10 dark:to-red-500/5',
      ringColorClass: 'ring-red-500/20 dark:ring-red-400/20',
    },
    rose: {
      iconForeground: 'text-rose-500 dark:text-rose-400',
      iconBackground:
        'bg-gradient-to-br from-rose-100 via-rose-50 to-white dark:from-rose-500/20 dark:via-rose-500/10 dark:to-rose-500/5',
      ringColorClass: 'ring-rose-500/20 dark:ring-rose-400/20',
    },
    teal: {
      iconForeground: 'text-teal-500 dark:text-teal-400',
      iconBackground:
        'bg-gradient-to-br from-teal-100 via-teal-50 to-white dark:from-teal-500/20 dark:via-teal-500/10 dark:to-teal-500/5',
      ringColorClass: 'ring-teal-500/20 dark:ring-teal-400/20',
    },
    sky: {
      iconForeground: 'text-sky-500 dark:text-sky-400',
      iconBackground:
        'bg-gradient-to-br from-sky-100 via-sky-50 to-white dark:from-sky-500/20 dark:via-sky-500/10 dark:to-sky-500/5',
      ringColorClass: 'ring-sky-500/20 dark:ring-sky-400/20',
    },
    zinc: {
      iconForeground: 'text-zinc-500 dark:text-zinc-400',
      iconBackground:
        'bg-gradient-to-br from-zinc-100 via-zinc-50 to-white dark:from-zinc-500/20 dark:via-zinc-500/10 dark:to-zinc-500/5',
      ringColorClass: 'ring-zinc-500/20 dark:ring-zinc-400/20',
    },
    stone: {
      iconForeground: 'text-stone-500 dark:text-stone-400',
      iconBackground:
        'bg-gradient-to-br from-stone-100 via-stone-50 to-white dark:from-stone-500/20 dark:via-stone-500/10 dark:to-stone-500/5',
      ringColorClass: 'ring-stone-500/20 dark:ring-stone-400/20',
    },
    lime: {
      iconForeground: 'text-lime-500 dark:text-lime-400',
      iconBackground:
        'bg-gradient-to-br from-lime-100 via-lime-50 to-white dark:from-lime-500/20 dark:via-lime-500/10 dark:to-lime-500/5',
      ringColorClass: 'ring-lime-500/20 dark:ring-lime-400/20',
    },
  };

  return (
    colorMap[colorClass] || {
      iconForeground: 'text-gray-500 dark:text-gray-400',
      iconBackground:
        'bg-gradient-to-br from-gray-100 via-gray-50 to-white dark:from-gray-500/20 dark:via-gray-500/10 dark:to-gray-500/5',
      ringColorClass: 'ring-gray-500/20 dark:ring-gray-400/20',
    }
  );
};

export function PlatformNavCards({ basePath = '' }: PlatformNavCardsProps) {
  return (
    <div className="overflow-hidden rounded-[1rem] grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-4 space-y-0.5 sm:space-y-0 p-0.5">
      {platformNavItems.map((item) => {
        const colorConfig = getColorConfig(item.color);
        const Icon = item.icon;
        
        return (
          <Card
            key={item.href}
            className={cn(
              'bg-gradient-to-bl from-background to-muted/80 shadow-xs group relative rounded-xl border p-0 focus-within:ring-2 focus-within:ring-ring focus-within:ring-inset'
            )}
          >
            <CardContent className="p-4">
              <div>
                {Icon && (
                  <span
                    className={cn(
                      colorConfig.iconBackground,
                      colorConfig.iconForeground,
                      'inline-flex rounded-lg p-2 ring-1 ring-inset',
                      colorConfig.ringColorClass
                    )}
                  >
                    <Icon aria-hidden="true" className="h-4 w-4" />
                  </span>
                )}
              </div>
              <div className="mt-3">
                <h3 className="text-sm font-semibold text-foreground">
                  <Link href={`${basePath}${item.href}`} className="focus:outline-none">
                    <span aria-hidden="true" className="absolute inset-0" />
                    {item.title}
                  </Link>
                </h3>
                <p className="mt-1 text-xs text-muted-foreground line-clamp-2">
                  {item.description}
                </p>
              </div>
              <span
                aria-hidden="true"
                className="pointer-events-none absolute top-4 right-4 text-muted-foreground/50 group-hover:text-muted-foreground/60"
              >
                <ArrowUpRight className="h-4 w-4" />
              </span>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}