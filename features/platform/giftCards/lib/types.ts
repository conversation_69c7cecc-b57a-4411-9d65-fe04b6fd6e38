export interface GiftCard {
  id: string;
  code?: string | null;
  value?: number | null;
  balance?: number | null;
  isDisabled?: boolean | null;
  endsAt?: string | null;
  metadata?: any | null;
  order?: Order | null;
  giftCardTransactions?: GiftCardTransaction[];
  giftCardTransactionsCount?: number;
  region?: Region | null;
  createdAt?: string | null;
  updatedAt?: string | null;
}

export interface GiftCardTransaction {
  id: string;
  amount?: number | null;
  isTaxable?: boolean | null;
  taxRate?: number | null;
  order?: Order | null;
  createdAt?: string | null;
}

export interface Order {
  id: string;
  displayId?: string | null;
  status?: string | null;
  total?: number | null;
  createdAt?: string | null;
  email?: string | null;
  currency?: {
    code: string;
  } | null;
}

export interface Region {
  id: string;
  name: string;
  currencyCode: string;
}

export interface GiftCardStatusCounts {
  all: number;
  active: number;
  disabled: number;
  depleted: number;
  expired: number;
}

export interface GiftCardUpdateInput {
  code?: string | null;
  value?: number | null;
  balance?: number | null;
  isDisabled?: boolean | null;
  endsAt?: string | null;
  metadata?: any | null;
  region?: {
    connect?: { id: string };
    disconnect?: boolean;
  };
}

export interface GiftCardCreateInput {
  code: string;
  value: number;
  balance: number;
  isDisabled?: boolean | null;
  endsAt?: string | null;
  metadata?: any | null;
  region?: {
    connect?: { id: string };
  };
  order?: {
    connect?: { id: string };
  };
}

// Sorting option interface
export interface SortOption {
  field: string;
  direction: 'ASC' | 'DESC';
}

// Response interfaces
export interface ApiListResponse<T> {
  items: T[];
  count: number;
}

export interface MutationResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}