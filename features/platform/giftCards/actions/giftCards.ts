"use server";

import { keystoneClient } from "../../../dashboard/lib/keystoneClient";
import {
  SortOption,
  GiftCard,
  GiftCardStatusCounts,
  GiftCardUpdateInput,
  GiftCardCreateInput,
  MutationResponse,
  ApiListResponse,
} from "../lib/types";

type KeystoneClientResponse<T> = 
  | { success: true; data: T; error?: never }
  | { success: false; data?: T; error: any };

export async function getFilteredGiftCards(
  status: 'active' | 'disabled' | 'depleted' | 'expired' | null,
  search: string | null,
  page: number,
  pageSize: number,
  sort: SortOption | null
): Promise<KeystoneClientResponse<ApiListResponse<GiftCard>>> {
  const skip = (page - 1) * pageSize;
  const take = pageSize;

  let whereClauses: any[] = [];
  
  const now = new Date().toISOString();
  
  if (status === 'active') {
    whereClauses.push({
      AND: [
        { isDisabled: { equals: false } },
        { balance: { gt: 0 } },
        {
          OR: [
            { endsAt: { gte: now } },
            { endsAt: { equals: null } }
          ]
        }
      ]
    });
  } else if (status === 'disabled') {
    whereClauses.push({ isDisabled: { equals: true } });
  } else if (status === 'depleted') {
    whereClauses.push({ balance: { lte: 0 } });
  } else if (status === 'expired') {
    whereClauses.push({
      AND: [
        { endsAt: { not: { equals: null } } },
        { endsAt: { lt: now } }
      ]
    });
  }
  
  if (search) {
    whereClauses.push({
      code: { contains: search, mode: 'insensitive' }
    });
  }

  const where = whereClauses.length > 0 ? { AND: whereClauses } : {};
  
  const orderBy = sort ? { [sort.field]: sort.direction.toLowerCase() } : { createdAt: 'desc' };

  const query = `
    query GetFilteredGiftCards(
      $where: GiftCardWhereInput
      $take: Int
      $skip: Int
      $orderBy: [GiftCardOrderByInput!]
    ) {
      items: giftCards(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        id
        code
        value
        balance
        isDisabled
        endsAt
        metadata
        createdAt
        updatedAt
        giftCardTransactionsCount
        order {
          id
          displayId
          email
          createdAt
        }
        region {
          id
          name
          currencyCode
        }
        giftCardTransactions(take: 10, orderBy: { createdAt: desc }) {
          id
          amount
          isTaxable
          taxRate
          createdAt
          order {
            id
            displayId
            email
          }
        }
      }
      count: giftCardsCount(where: $where)
    }
  `;

  const variables = { where, take, skip, orderBy: [orderBy] };
  
  return keystoneClient<ApiListResponse<GiftCard>>(query, variables);
}

export async function getGiftCardStatusCounts(): Promise<KeystoneClientResponse<GiftCardStatusCounts>> {
  const now = new Date().toISOString();
  
  const query = `
    query GetGiftCardStatusCounts($now: DateTime!) {
      all: giftCardsCount
      disabled: giftCardsCount(where: { isDisabled: { equals: true } })
      depleted: giftCardsCount(where: { balance: { lte: 0 } })
      active: giftCardsCount(where: {
        AND: [
          { isDisabled: { equals: false } },
          { balance: { gt: 0 } },
          {
            OR: [
              { endsAt: { gte: $now } },
              { endsAt: { equals: null } }
            ]
          }
        ]
      })
      expired: giftCardsCount(where: {
        AND: [
          { endsAt: { not: { equals: null } } },
          { endsAt: { lt: $now } }
        ]
      })
    }
  `;

  return keystoneClient<GiftCardStatusCounts>(query, { now });
}

export async function getGiftCard(id: string): Promise<KeystoneClientResponse<{ giftCard: GiftCard | null }>> {
  const query = `
    query GetGiftCard($id: ID!) {
      giftCard(where: { id: $id }) {
        id
        code
        value
        balance
        isDisabled
        endsAt
        metadata
        createdAt
        updatedAt
        giftCardTransactionsCount
        order {
          id
          displayId
          email
          status
          total
          createdAt
          currency {
            code
          }
        }
        region {
          id
          name
          currencyCode
        }
        giftCardTransactions(orderBy: { createdAt: desc }) {
          id
          amount
          isTaxable
          taxRate
          createdAt
          order {
            id
            displayId
            email
            status
          }
        }
      }
    }
  `;
  
  return keystoneClient<{ giftCard: GiftCard | null }>(query, { id });
}

export async function updateGiftCard(
  id: string,
  input: GiftCardUpdateInput
): Promise<MutationResponse<GiftCard>> {
  const query = `
    mutation UpdateGiftCard($id: ID!, $data: GiftCardUpdateInput!) {
      item: updateGiftCard(where: { id: $id }, data: $data) {
        id
        code
        value
        balance
        isDisabled
        endsAt
        metadata
        createdAt
        updatedAt
        region {
          id
          name
          currencyCode
        }
      }
    }
  `;
  try {
    const response = await keystoneClient<{ item: GiftCard | null }>(query, { id, data: input });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Gift card not found or update failed." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during update.";
    console.error(`Error in updateGiftCard for id ${id}:`, message);
    return { success: false, error: message };
  }
}

export async function deleteGiftCard(id: string): Promise<MutationResponse<{ id: string }>> {
  const query = `
    mutation DeleteGiftCard($id: ID!) {
      item: deleteGiftCard(where: { id: $id }) {
        id
      }
    }
  `;
  try {
    const response = await keystoneClient<{ item: { id: string } | null }>(query, { id });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Gift card not found or delete failed." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during delete.";
    console.error(`Error in deleteGiftCard for id ${id}:`, message);
    return { success: false, error: message };
  }
}

export async function createGiftCard(
  input: GiftCardCreateInput
): Promise<MutationResponse<GiftCard>> {
  const query = `
    mutation CreateGiftCard($data: GiftCardCreateInput!) {
      item: createGiftCard(data: $data) {
        id
        code
        value
        balance
        isDisabled
        endsAt
        metadata
        createdAt
        updatedAt
        region {
          id
          name
          currencyCode
        }
      }
    }
  `;
   try {
    const response = await keystoneClient<{ item: GiftCard }>(query, { data: input });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Failed to create gift card." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during creation.";
    console.error("Error in createGiftCard:", message);
    return { success: false, error: message };
  }
}