"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, Gift, CreditCard, Activity, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import type { GiftCard, GiftCardTransaction } from "../lib/types";

interface GiftCardDetailsComponentProps {
  giftCard: GiftCard;
}

export function GiftCardDetailsComponent({ giftCard }: GiftCardDetailsComponentProps) {
  const [isOpen, setIsOpen] = useState(false);

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  const formatCurrency = (amount: number | null | undefined, currencyCode?: string | null) => {
    if (amount === null || amount === undefined) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode || giftCard.region?.currencyCode || "USD",
    }).format(amount / 100);
  };

  const getGiftCardStatus = () => {
    const now = new Date();
    const endsAt = giftCard.endsAt ? new Date(giftCard.endsAt) : null;

    if (giftCard.isDisabled) return "disabled";
    if (giftCard.balance === 0) return "depleted";
    if (endsAt && endsAt < now) return "expired";
    return "active";
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "active":
        return "default";
      case "depleted":
        return "secondary";
      case "disabled":
      case "expired":
        return "destructive";
      default:
        return "outline";
    }
  };

  const status = getGiftCardStatus();
  const balancePercent = giftCard.value ? ((giftCard.balance || 0) / giftCard.value) * 100 : 0;

  return (
    <div className="px-4 py-4 hover:bg-muted/50 transition-colors">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              <Gift className="h-5 w-5 text-primary" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium font-mono">{giftCard.code || "No Code"}</h3>
                <Badge variant={getStatusBadgeVariant(status)}>
                  {status}
                </Badge>
                {giftCard.region && (
                  <Badge variant="outline" className="text-xs">
                    {giftCard.region.name}
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>
                  Balance: {formatCurrency(giftCard.balance)} / {formatCurrency(giftCard.value)}
                </span>
                {giftCard.endsAt && (
                  <span>Expires: {formatDate(giftCard.endsAt)}</span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right mr-4">
              <div className="text-sm font-medium">
                {giftCard.giftCardTransactionsCount || 0} transactions
              </div>
              <div className="text-xs text-muted-foreground">
                {giftCard.order ? `From order #${giftCard.order.displayId || giftCard.order.id.slice(-8)}` : "Manual"}
              </div>
            </div>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-9 p-0">
                {isOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
                <span className="sr-only">Toggle details</span>
              </Button>
            </CollapsibleTrigger>
          </div>
        </div>
        <CollapsibleContent className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Balance & Usage Section */}
            <Card className="p-4">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Balance & Usage
              </h4>
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Balance Progress</span>
                    <span className="font-medium">
                      {formatCurrency(giftCard.balance)} / {formatCurrency(giftCard.value)}
                    </span>
                  </div>
                  <Progress value={balancePercent} className="h-2" />
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Original Value:</span>
                    <span>{formatCurrency(giftCard.value)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Current Balance:</span>
                    <span className="font-medium">{formatCurrency(giftCard.balance)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Used:</span>
                    <span>
                      {formatCurrency((giftCard.value || 0) - (giftCard.balance || 0))}
                    </span>
                  </div>
                </div>
                {giftCard.order && (
                  <div className="pt-2 border-t">
                    <div className="text-xs text-muted-foreground">Purchased with order:</div>
                    <div className="text-sm font-medium">
                      #{giftCard.order.displayId || giftCard.order.id.slice(-8)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(giftCard.order.createdAt)}
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* Transaction History Section */}
            <Card className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Transaction History
                </h4>
                <Badge variant="secondary">{giftCard.giftCardTransactionsCount || 0}</Badge>
              </div>
              <ScrollArea className="h-[150px]">
                {giftCard.giftCardTransactions && giftCard.giftCardTransactions.length > 0 ? (
                  <div className="space-y-2">
                    {giftCard.giftCardTransactions.map((transaction: GiftCardTransaction) => (
                      <div
                        key={transaction.id}
                        className="p-2 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-sm">
                              {formatCurrency(transaction.amount)}
                            </div>
                            {transaction.order && (
                              <div className="text-xs text-muted-foreground">
                                Order #{transaction.order.displayId || transaction.order.id.slice(-8)}
                              </div>
                            )}
                          </div>
                          <div className="text-right">
                            <div className="text-xs text-muted-foreground">
                              {formatDateTime(transaction.createdAt)}
                            </div>
                            {transaction.isTaxable && (
                              <div className="text-xs">
                                Tax: {((transaction.taxRate || 0) * 100).toFixed(2)}%
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-sm text-muted-foreground py-4">
                    No transactions yet
                  </div>
                )}
              </ScrollArea>
            </Card>
          </div>

          {/* Metadata */}
          {giftCard.metadata && Object.keys(giftCard.metadata).length > 0 && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center gap-2 text-sm">
                <span className="text-muted-foreground">Metadata:</span>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  {JSON.stringify(giftCard.metadata)}
                </code>
              </div>
            </div>
          )}

          {/* Additional Info */}
          <div className="mt-4 pt-4 border-t flex items-center justify-between text-xs text-muted-foreground">
            <div>
              Created: {formatDate(giftCard.createdAt)}
            </div>
            <div>
              Updated: {formatDate(giftCard.updatedAt)}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}