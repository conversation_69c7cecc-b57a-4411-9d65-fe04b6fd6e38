"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface StatusTabsProps {
  statusCounts: {
    all: number;
    active: number;
    disabled: number;
    depleted: number;
    expired: number;
  };
}

export function StatusTabs({ statusCounts }: StatusTabsProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const statusFilter = searchParams.get("!status_matches");
  let activeTab = "all";
  
  if (statusFilter) {
    try {
      const parsed = JSON.parse(decodeURIComponent(statusFilter));
      if (Array.isArray(parsed) && parsed.length > 0) {
        activeTab = parsed[0].value;
      }
    } catch (e) {
      // Invalid JSON, ignore
    }
  }

  const handleTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Remove existing filters
    params.delete("!status_matches");
    params.delete("page"); // Reset to first page when changing filters
    
    if (value !== "all") {
      params.set("!status_matches", encodeURIComponent(JSON.stringify([{ value }])));
    }
    
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="px-4 md:px-6 overflow-x-auto">
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="grid w-full max-w-2xl grid-cols-5">
          <TabsTrigger value="all" className="text-xs md:text-sm">
            All
            <span className="ml-1.5 rounded-full bg-muted px-1.5 py-0.5 text-xs font-medium">
              {statusCounts.all}
            </span>
          </TabsTrigger>
          <TabsTrigger value="active" className="text-xs md:text-sm">
            Active
            <span className="ml-1.5 rounded-full bg-muted px-1.5 py-0.5 text-xs font-medium">
              {statusCounts.active}
            </span>
          </TabsTrigger>
          <TabsTrigger value="disabled" className="text-xs md:text-sm">
            Disabled
            <span className="ml-1.5 rounded-full bg-muted px-1.5 py-0.5 text-xs font-medium">
              {statusCounts.disabled}
            </span>
          </TabsTrigger>
          <TabsTrigger value="depleted" className="text-xs md:text-sm">
            Depleted
            <span className="ml-1.5 rounded-full bg-muted px-1.5 py-0.5 text-xs font-medium">
              {statusCounts.depleted}
            </span>
          </TabsTrigger>
          <TabsTrigger value="expired" className="text-xs md:text-sm">
            Expired
            <span className="ml-1.5 rounded-full bg-muted px-1.5 py-0.5 text-xs font-medium">
              {statusCounts.expired}
            </span>
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}