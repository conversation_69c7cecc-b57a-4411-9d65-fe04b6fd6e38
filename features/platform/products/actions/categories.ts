'use server';

import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

/**
 * Get list of product categories
 */
export async function getCategories(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id
    title
    handle
    isActive
    isInternal
    metadata
    createdAt
    updatedAt
    parentCategory {
      id
      title
    }
    categoryChildren {
      id
      title
    }
  `
) {
  const query = `
    query GetCategories($where: ProductCategoryWhereInput, $take: Int!, $skip: Int!, $orderBy: [ProductCategoryOrderByInput!]) {
      items: productCategories(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: productCategoriesCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, { where, take, skip, orderBy });
  return response;
}

/**
 * Get a single category with its products
 */
export async function getCategoryWithProducts(
  categoryId: string,
  productPage: number = 1,
  productPageSize: number = 10
) {
  const productSkip = (productPage - 1) * productPageSize;
  
  const query = `
    query GetCategoryWithProducts($id: ID!, $productTake: Int!, $productSkip: Int!) {
      category: productCategory(where: { id: $id }) {
        id
        title
        handle
        isActive
        isInternal
        metadata
        createdAt
        updatedAt
        parentCategory {
          id
          title
        }
        categoryChildren {
          id
          title
        }
        products(take: $productTake, skip: $productSkip, orderBy: [{ createdAt: desc }]) {
          id
          title
          handle
          status
          thumbnail
          createdAt
          productVariants {
            id
            title
            sku
            inventoryQuantity
          }
        }
      }
      productCount: productsCount(where: { productCategories: { some: { id: { equals: $id } } } })
    }
  `;

  const response = await keystoneClient(query, { 
    id: categoryId, 
    productTake: productPageSize, 
    productSkip 
  });
  
  return response;
}

/**
 * Get filtered categories
 */
export async function getFilteredCategories(
  isActive: boolean | null = null,
  search: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add active filter if provided
  if (isActive !== null) {
    where.isActive = { equals: isActive };
  }

  // Add search filter if provided
  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { handle: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getCategories(where, pageSize, skip, orderBy);
}

/**
 * Get category counts by status
 */
export async function getCategoryStatusCounts() {
  const query = `
    query GetCategoryStatusCounts {
      active: productCategoriesCount(where: { isActive: { equals: true } })
      inactive: productCategoriesCount(where: { isActive: { equals: false } })
      all: productCategoriesCount
    }
  `;

  const response = await keystoneClient(query);
  return response;
}