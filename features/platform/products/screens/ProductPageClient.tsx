"use client";

import { use<PERSON><PERSON>back, useState, useMemo, useId } from "react";
import {
  <PERSON>ert<PERSON>ircle,
  AlertTriangleIcon,
  Check,
  Copy,
  Loader2,
  Undo2,
  X,
  Package,
  Image,
  Box,
  Tag,
  Building,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

import type { FieldMeta } from "@/features/dashboard/types";
import { useListByPath } from "@/features/dashboard/hooks/useAdminMeta";
import { deleteItem } from "@/features/dashboard/actions";
import { toast } from "sonner";
import { Fields } from "@/features/dashboard/components/Fields";
import { makeDataGetter } from "@/features/dashboard/lib/dataGetter";
import { useItemForm } from "@/features/dashboard/lib/useItemForm";
import type { ItemData } from "@/features/dashboard/lib/serialization";
import { VariantsTab } from "../components/VariantsTab";

// Tab configuration
const tabs = [
  {
    id: "general",
    label: "General",
    icon: Package,
    description: "Basic product information like title, description, and handle",
  },
  {
    id: "media",
    label: "Media",
    icon: Image,
    description: "Product images and gallery",
  },
  {
    id: "variants",
    label: "Variants",
    icon: Box,
    description: "Product variations, options, and inventory",
  },
  {
    id: "discounts",
    label: "Discounts & Taxes",
    icon: Tag,
    description: "Pricing rules, discounts, and tax settings",
  },
  {
    id: "organization",
    label: "Organization",
    icon: Building,
    description: "Categories, collections, tags, and status",
  },
];

// Field groups for each tab
const GENERAL_FIELDS = [
  "title",
  "handle",
  "description",
  "subtitle",
  "isGiftcard",
];

const MEDIA_FIELDS = ["productImages"];

const VARIANT_FIELDS = ["productVariants"];

const DISCOUNT_TAX_FIELDS = [
  "discountable",
  "discountConditions",
  "discountRules",
  "taxRates",
];

const ORGANIZATION_FIELDS = [
  "status",
  "productCollections",
  "productCategories",
  "productTags",
];

// Loading skeleton
function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Skeleton className="h-12 w-12 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
      </div>
      <div className="space-y-8">
        {[1, 2, 3].map((i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
    </div>
  );
}

interface ProductPageClientProps {
  item: Record<string, unknown>;
  id: string;
  fieldModes: Record<string, "read" | "edit" | "hidden">;
  fieldPositions: Record<string, "form" | "sidebar">;
  uiConfig?: {
    hideDelete?: boolean;
  };
  listKey: string;
}

export function ProductPageClient({
  item,
  id,
  fieldModes,
  fieldPositions,
  uiConfig,
  listKey,
}: ProductPageClientProps) {
  const router = useRouter();
  const { list, isLoading } = useListByPath(listKey);
  const [saveState, setSaveState] = useState<"idle" | "saving" | "saved">("idle");
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const selectId = useId();

  // Pre-compute values that don't depend on list to avoid React Hook conditionals
  const itemWithId = item as ItemData;
  const dataGetter = makeDataGetter(itemWithId, undefined);

  // Get selected fields - defined outside conditional block
  const selectedFields = useMemo(() => {
    if (!list) return "";
    return Object.keys(list.fields)
      .map((key) => list.fields[key].controller?.graphqlSelection)
      .filter(Boolean)
      .join("\n");
  }, [list]);

  // Initialize form state - defined outside conditional block with proper type definition
  const itemForm = useItemForm({
    list:
      list ||
      ({ fields: {}, key: listKey } as {
        fields: Record<string, FieldMeta>;
        key: string;
      }), // Using a minimal type definition
    fields: list?.fields || {},
    selectedFields,
    itemGetter: dataGetter,
  });

  // Define callback outside conditional block with proper deps
  const handleValueChange = useCallback(
    (valueUpdater: (prevValue: Value) => Value) => {
      itemForm.setValue((state) => ({
        item: state.item,
        value: valueUpdater(state.value),
      }));
    },
    [itemForm] // Include itemForm in deps
  );

  // Get fields for current tab
  const getFieldsForTab = useCallback(
    (tabId: string): Record<string, FieldMeta> => {
      if (!list) return {};

      let fieldKeys: string[] = [];
      switch (tabId) {
        case "general":
          fieldKeys = GENERAL_FIELDS;
          break;
        case "media":
          fieldKeys = MEDIA_FIELDS;
          break;
        case "variants":
          fieldKeys = VARIANT_FIELDS;
          break;
        case "discounts":
          fieldKeys = DISCOUNT_TAX_FIELDS;
          break;
        case "organization":
          fieldKeys = ORGANIZATION_FIELDS;
          break;
      }

      const fields: Record<string, FieldMeta> = {};
      fieldKeys.forEach((key) => {
        if (list.fields[key]) {
          fields[key] = list.fields[key];
        }
      });

      return fields;
    },
    [list]
  );

  // Check if a tab has errors and count them
  const getTabErrorCount = useCallback(
    (tabId: string) => {
      const tabFields = getFieldsForTab(tabId);
      const fieldKeys = Object.keys(tabFields);

      // Count how many of the tab's fields are in the invalidFields set
      return fieldKeys.filter((fieldKey) =>
        itemForm.invalidFields.has(fieldKey)
      ).length;
    },
    [getFieldsForTab, itemForm.invalidFields]
  );

  // Split fields based on position - but only for sidebar fields
  const fieldsSplit = useMemo(() => {
    const sidebarFields: Record<string, FieldMeta> = {};

    if (list) {
      Object.entries(list.fields).forEach(([key, field]) => {
        if (fieldPositions[key] === "sidebar") {
          sidebarFields[key] = field;
        }
      });
    }

    return { sidebarFields };
  }, [list, fieldPositions]);

  // Early return for loading or missing list
  if (isLoading || !list) {
    return <LoadingSkeleton />;
  }

  const itemLabel = (item[list.labelField] as string) || id;

  const handleSave = async () => {
    // Check if there are validation errors
    if (itemForm.invalidFields.size > 0) {
      // Find the first tab with errors and switch to it
      for (const tab of tabs) {
        const errorCount = getTabErrorCount(tab.id);
        if (errorCount > 0) {
          setActiveTab(tab.id);
          toast.error(
            `Please fix ${errorCount} error${errorCount > 1 ? "s" : ""} in ${
              tab.label
            } section`
          );
          break;
        }
      }
      return;
    }

    setSaveState("saving");
    try {
      await itemForm.onSave();
      setSaveState("saved");
      setTimeout(() => setSaveState("idle"), 3000);
    } catch {
      setSaveState("idle");
      toast.error("Failed to save changes");
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await deleteItem(list.key, id, {
        deleteMutationName: list.gqlNames?.deleteMutationName || "",
      });

      if (response.success) {
        toast.success(`Deleted ${list?.singular || "item"} successfully`);
        router.push(`/${list?.path || ""}`);
      } else {
        toast.error(`Failed to delete: ${response.error || "Unknown error"}`);
        setIsDeleting(false);
      }
    } catch (err) {
      console.error("Delete error:", err);
      toast.error("Failed to delete item");
      setIsDeleting(false);
    }
  };

  const handleCopyId = () => {
    navigator.clipboard.writeText(id);
    toast.success("ID copied to clipboard");
  };

  return (
    <main className="w-full max-w-5xl p-4 md:p-6 pb-16 lg:pb-6">
      <div className="grid lg:grid-cols-[minmax(240px,2fr)_3fr] gap-4 lg:gap-6 lg:gap-y-8 min-h-0 lg:min-h-[calc(100vh-8rem)]">
        {/* Sidebar */}
        <aside className="lg:sticky lg:top-24 lg:self-start lg:max-h-[calc(100vh-7.5rem)] flex flex-col h-full">
          <div className="flex-grow overflow-y-auto pb-2">
            <div className="mb-6">
              <h1
                className="text-lg font-semibold md:text-2xl"
                title={itemLabel}
              >
                {itemLabel}
              </h1>
              <div className="mt-6">
                <div className="relative border rounded-md bg-muted/40 transition-all">
                  <div className="p-1 flex items-center gap-3">
                    <div className="flex gap-3 flex-1 min-w-0">
                      <div className="flex-shrink-0">
                        <div className="bg-background shadow-xs border rounded-sm py-0.5 px-1 text-[.65rem] text-muted-foreground">
                          ID
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-mono truncate">{id}</div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-sm h-6 w-6 flex-shrink-0"
                      onClick={handleCopyId}
                    >
                      <Copy className="size-3" />
                      <span className="sr-only">Copy ID</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile Select - hidden on lg screens and up */}
            <div className="space-y-2 mb-4 lg:hidden">
              <div className="mb-1">
                <Label htmlFor={selectId} className="mb-2">
                  Section
                </Label>
              </div>
              <Select value={activeTab} onValueChange={setActiveTab}>
                <SelectTrigger
                  id={selectId}
                  className={`h-auto py-3 ps-3 pe-4 text-left [&>span]:flex [&>span]:items-center [&>span]:gap-3 [&>span_svg]:shrink-0 ${
                    getTabErrorCount(activeTab) > 0
                      ? "border-rose-500 bg-rose-50 dark:bg-rose-900/20"
                      : "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                  }`}
                >
                  <SelectValue>
                    {(() => {
                      const currentTab = tabs.find((t) => t.id === activeTab);
                      if (!currentTab) return null;
                      const Icon = currentTab.icon;
                      const errorCount = getTabErrorCount(activeTab);
                      const hasError = errorCount > 0;

                      return (
                        <span className="flex gap-3">
                          <Icon
                            className={`mt-1 size-4 shrink-0 ${
                              hasError
                                ? "text-rose-600 dark:text-rose-400"
                                : "text-blue-600 dark:text-blue-400"
                            }`}
                          />
                          <span className="flex-1">
                            <span
                              className={`block font-medium text-sm ${
                                hasError
                                  ? "text-rose-600 dark:text-rose-400"
                                  : "text-blue-600 dark:text-blue-400"
                              }`}
                            >
                              {currentTab.label}
                              {hasError && (
                                <span className="ml-2">
                                  ({errorCount} error{errorCount > 1 ? "s" : ""}
                                  )
                                </span>
                              )}
                            </span>
                            <span className="text-muted-foreground mt-0.5 block text-xs">
                              {currentTab.description}
                            </span>
                          </span>
                        </span>
                      );
                    })()}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent className="[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    const errorCount = getTabErrorCount(tab.id);
                    const hasError = errorCount > 0;

                    return (
                      <SelectItem key={tab.id} value={tab.id}>
                        <span className="flex gap-3">
                          <Icon
                            className={`mt-1 size-4 shrink-0 ${
                              hasError
                                ? "text-rose-600 dark:text-rose-400"
                                : "text-muted-foreground"
                            }`}
                          />
                          <span>
                            <span className="block font-medium">
                              {tab.label}
                              {hasError && (
                                <span className="ml-2 text-rose-600 dark:text-rose-400">
                                  ({errorCount} error{errorCount > 1 ? "s" : ""}
                                  )
                                </span>
                              )}
                            </span>
                            <span className="text-muted-foreground mt-0.5 block text-xs">
                              {tab.description}
                            </span>
                          </span>
                        </span>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Desktop Tab Cards - hidden on smaller screens */}
            <div className="hidden lg:block space-y-3 mb-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;
                const errorCount = getTabErrorCount(tab.id);
                const hasError = errorCount > 0;

                return (
                  <div
                    key={tab.id}
                    className={`relative border rounded-md transition-all cursor-pointer ${
                      isActive && hasError
                        ? "border-rose-500 bg-rose-50 dark:bg-rose-900/20"
                        : isActive
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : hasError
                        ? "border-rose-300 dark:border-rose-800 bg-rose-50/50 dark:bg-rose-900/10"
                        : "bg-muted/40 hover:border-blue-200 dark:hover:border-blue-800"
                    }`}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    <div className="p-3 flex items-start justify-between gap-3">
                      <div className="flex gap-3 flex-1">
                        <div className="flex-shrink-0 mt-[2px]">
                          <Icon
                            className={`h-4 w-4 ${
                              isActive && hasError
                                ? "text-rose-600 dark:text-rose-400"
                                : isActive
                                ? "text-blue-600 dark:text-blue-400"
                                : hasError
                                ? "text-rose-600 dark:text-rose-400"
                                : "text-muted-foreground"
                            }`}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div
                            className={`font-medium text-sm ${
                              isActive && hasError
                                ? "text-rose-600 dark:text-rose-400"
                                : isActive
                                ? "text-blue-600 dark:text-blue-400"
                                : hasError
                                ? "text-rose-600 dark:text-rose-400"
                                : ""
                            }`}
                          >
                            {tab.label}
                          </div>
                          <div className="text-xs text-muted-foreground mt-0.5">
                            {tab.description}
                          </div>
                        </div>
                      </div>
                      {hasError && (
                        <Badge
                          color="rose"
                          className="px-2 h-5 text-xs font-medium uppercase flex-shrink-0 flex items-center justify-center"
                        >
                          {errorCount} ERROR{errorCount > 1 ? "S" : ""}
                        </Badge>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Sidebar Fields */}
            {Object.keys(fieldsSplit.sidebarFields).length > 0 && (
              <Fields
                fields={fieldsSplit.sidebarFields}
                fieldModes={fieldModes}
                fieldPositions={fieldPositions}
                value={itemForm.state.value}
                onChange={handleValueChange}
                forceValidation={itemForm.forceValidation}
                invalidFields={itemForm.invalidFields}
              />
            )}
          </div>

          {/* Action buttons - visible only on larger screens */}
          <div className="hidden lg:flex flex-col mr-auto">
            {/* Status indicators above buttons */}
            <div className="flex justify-center mb-2">
              {saveState === "saving" && (
                <div className="flex items-center gap-x-1.5 text-xs text-muted-foreground">
                  <Loader2 className="animate-spin h-3.5 w-3.5" />
                  <span>Saving...</span>
                </div>
              )}
              {saveState === "saved" && (
                <div className="flex items-center gap-x-1.5 text-xs text-emerald-500">
                  <Check className="h-3.5 w-3.5" />
                  <span>Saved</span>
                </div>
              )}
            </div>

            {/* Buttons */}
            <div className="flex flex-wrap items-center gap-2">
              {!uiConfig?.hideDelete && (
                <Button
                  variant="destructive"
                  onClick={() => {
                    const deleteTrigger =
                      document.querySelector<HTMLButtonElement>(
                        "[data-delete-trigger]"
                      );
                    deleteTrigger?.click();
                  }}
                  disabled={itemForm.loading || saveState === "saving"}
                >
                  <X className="size-4 shrink-0" />
                  Delete
                </Button>
              )}
              {itemForm.changedFields.size > 0 && (
                <Button
                  variant="outline"
                  onClick={itemForm.onReset}
                  disabled={itemForm.loading || saveState === "saving"}
                >
                  <Undo2 className="size-4 shrink-0" />
                  Reset
                </Button>
              )}
              <Button
                onClick={handleSave}
                disabled={
                  !(itemForm.changedFields.size > 0) ||
                  itemForm.loading ||
                  saveState === "saving"
                }
              >
                Save Changes
                <Check className="ml-1 stroke-[1.5px]" width="10" height="10" />
              </Button>
            </div>
          </div>
        </aside>

        {/* Floating action bar - visible only on smaller screens */}
        <div className="fixed bottom-4 left-1/2 -translate-x-1/2 z-10 lg:hidden flex flex-col items-center gap-1.5">
          {/* Status indicators above the button container */}
          <div className="flex justify-center">
            {saveState === "saving" && (
              <div className="flex items-center gap-x-1 text-[10px] text-muted-foreground">
                <Loader2 className="animate-spin h-3 w-3" />
                <span>Saving...</span>
              </div>
            )}
            {saveState === "saved" && (
              <div className="flex items-center gap-x-1 text-[10px] text-emerald-500">
                <Check className="h-3 w-3" />
                <span>Saved</span>
              </div>
            )}
          </div>

          {/* Button container */}
          <div className="bg-background border rounded-md px-1.5 py-1 shadow-md w-full">
            <div className="flex flex-wrap items-center gap-1">
              {!uiConfig?.hideDelete && (
                <Button
                  variant="destructive"
                  className="h-7 px-2 text-xs"
                  onClick={() => {
                    const deleteTrigger =
                      document.querySelector<HTMLButtonElement>(
                        "[data-delete-trigger]"
                      );
                    deleteTrigger?.click();
                  }}
                  disabled={itemForm.loading || saveState === "saving"}
                >
                  <X className="size-3 shrink-0" />
                  Delete
                </Button>
              )}
              {itemForm.changedFields.size > 0 && (
                <Button
                  variant="outline"
                  className="h-7 px-2 text-xs"
                  onClick={itemForm.onReset}
                  disabled={itemForm.loading || saveState === "saving"}
                >
                  <Undo2 className="size-3 shrink-0" />
                  Reset
                </Button>
              )}
              <Button
                className="h-7 px-2 text-xs"
                onClick={handleSave}
                disabled={
                  !(itemForm.changedFields.size > 0) ||
                  itemForm.loading ||
                  saveState === "saving"
                }
              >
                Save
                <Check className="ml-0.5 stroke-[1.5px]" width="7" height="7" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main content with proper tabs */}
        <div className="space-y-6">
          {itemForm.error && (
            <Badge color="rose" className="items-start gap-4 border p-4">
              <AlertCircle className="h-5 w-5" />
              <div>
                <h2 className="font-medium">Error</h2>
                <p>{itemForm.error.message}</p>
              </div>
            </Badge>
          )}

          {/* Proper Tabs Implementation */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
     

            {/* Tab Contents */}
            <TabsContent value="general" className="mt-6">
              <Fields
                fields={getFieldsForTab("general")}
                fieldModes={fieldModes}
                fieldPositions={fieldPositions}
                value={itemForm.state.value}
                onChange={handleValueChange}
                forceValidation={itemForm.forceValidation}
                invalidFields={itemForm.invalidFields}
              />
            </TabsContent>

            <TabsContent value="media" className="mt-6">
              <Fields
                fields={getFieldsForTab("media")}
                fieldModes={fieldModes}
                fieldPositions={fieldPositions}
                value={itemForm.state.value}
                onChange={handleValueChange}
                forceValidation={itemForm.forceValidation}
                invalidFields={itemForm.invalidFields}
              />
            </TabsContent>

            <TabsContent value="variants" className="mt-6">
              <VariantsTab product={item} />
            </TabsContent>

            <TabsContent value="discounts" className="mt-6">
              <Fields
                fields={getFieldsForTab("discounts")}
                fieldModes={fieldModes}
                fieldPositions={fieldPositions}
                value={itemForm.state.value}
                onChange={handleValueChange}
                forceValidation={itemForm.forceValidation}
                invalidFields={itemForm.invalidFields}
              />
            </TabsContent>

            <TabsContent value="organization" className="mt-6">
              <Fields
                fields={getFieldsForTab("organization")}
                fieldModes={fieldModes}
                fieldPositions={fieldPositions}
                value={itemForm.state.value}
                onChange={handleValueChange}
                forceValidation={itemForm.forceValidation}
                invalidFields={itemForm.invalidFields}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      {!uiConfig?.hideDelete && (
        <Dialog>
          <DialogTrigger asChild>
            <button className="hidden" data-delete-trigger>
              Delete
            </button>
          </DialogTrigger>
          <DialogContent>
            <div className="flex gap-4">
              <div>
                <Badge color="rose" className="rounded-full p-2 border">
                  <AlertTriangleIcon className="h-4 w-4" />
                </Badge>
              </div>
              <DialogHeader>
                <DialogTitle>Delete {itemLabel}?</DialogTitle>
                <DialogDescription>
                  This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline" className="rounded-lg">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                variant="destructive"
                className="rounded-lg"
                disabled={isDeleting}
                onClick={handleDelete}
              >
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </main>
  );
}

export default ProductPageClient;
