import {
  Package,
  Users,
  Tag,
  Gift,
  BadgeDollarSign,
  Clipboard,
  BarChart3,
  LayoutList,
  ArrowLeftRight,
  ShieldCheck,
  Truck,
  Settings,
  Ticket,
  LucideIcon,
} from 'lucide-react';

export interface PlatformNavItem {
  title: string;
  href: string;
  color: string;
  description: string;
  icon?: LucideIcon;
}

export const platformNavItems: PlatformNavItem[] = [
  {
    title: 'Orders',
    href: '/platform/orders',
    color: 'blue',
    description: 'Manage customer orders, track fulfillment, and process payments.',
    icon: Ticket,
  },
  {
    title: 'Products',
    href: '/platform/products',
    color: 'green',
    description: 'Create and manage your product catalog, pricing, and inventory.',
    icon: Package,
  },
  {
    title: 'Categories',
    href: '/platform/product-categories',
    color: 'lime',
    description: 'Organize products into categories and manage their hierarchy.',
    icon: Package,
  },
  {
    title: 'Users',
    href: '/platform/users',
    color: 'purple',
    description: 'Manage customer accounts, profiles, and user permissions.',
    icon: Users,
  },
  {
    title: 'Discounts',
    href: '/platform/discounts',
    color: 'pink',
    description: 'Create promotional codes, sales campaigns, and discount rules.',
    icon: Tag,
  },
  {
    title: 'Gift Cards',
    href: '/platform/gift-cards',
    color: 'orange',
    description: 'Issue and manage gift cards for customer purchases and rewards.',
    icon: Gift,
  },
  {
    title: 'Price Lists',
    href: '/platform/price-lists',
    color: 'emerald',
    description: 'Configure pricing strategies and customer-specific price tiers.',
    icon: BadgeDollarSign,
  },
  {
    title: 'Draft Orders',
    href: '/platform/draft-orders',
    color: 'stone',
    description: 'Create and manage draft orders before finalizing customer purchases.',
    icon: Clipboard,
  },
  {
    title: 'Analytics',
    href: '/platform/analytics',
    color: 'indigo',
    description: 'View sales reports, customer insights, and business performance metrics.',
    icon: BarChart3,
  },
  {
    title: 'Batch Jobs',
    href: '/platform/batch-jobs',
    color: 'cyan',
    description: 'Monitor and manage background tasks and bulk operations.',
    icon: LayoutList,
  },
  {
    title: 'Returns',
    href: '/platform/returns',
    color: 'red',
    description: 'Process customer returns, refunds, and exchange requests.',
    icon: ArrowLeftRight,
  },
  {
    title: 'Claims',
    href: '/platform/claims',
    color: 'rose',
    description: 'Handle customer claims, disputes, and resolution processes.',
    icon: ShieldCheck,
  },
  {
    title: 'Inventory',
    href: '/platform/inventory',
    color: 'teal',
    description: 'Track stock levels, manage warehouses, and monitor inventory.',
    icon: Package,
  },
  {
    title: 'Shipping',
    href: '/platform/shipping',
    color: 'sky',
    description: 'Configure shipping options, rates, and fulfillment methods.',
    icon: Truck,
  },
  {
    title: 'Settings',
    href: '/platform/settings',
    color: 'zinc',
    description: 'Configure platform settings, integrations, and system preferences.',
    icon: Settings,
  },
];

// Helper function to get platform nav items with full paths
export function getPlatformNavItemsWithBasePath(basePath: string) {
  return platformNavItems.map(item => ({
    ...item,
    href: `${basePath}${item.href}`,
  }));
}

// Helper function to get icon for a nav item by title
export function getIconForNavItem(title: string): LucideIcon {
  const item = platformNavItems.find(navItem => navItem.title === title);
  return item?.icon || Package;
}