"use server";

import { keystoneClient } from "../../../dashboard/lib/keystoneClient";
import {
  SortOption,
  Discount,
  DiscountStatusCounts,
  DiscountUpdateInput,
  DiscountCreateInput,
  MutationResponse,
  ApiListResponse,
} from "../lib/types";

type KeystoneClientResponse<T> = 
  | { success: true; data: T; error?: never }
  | { success: false; data?: T; error: any };

export async function getFilteredDiscounts(
  status: 'active' | 'disabled' | 'expired' | null,
  search: string | null,
  page: number,
  pageSize: number,
  sort: SortOption | null
): Promise<KeystoneClientResponse<ApiListResponse<Discount>>> {
  const skip = (page - 1) * pageSize;
  const take = pageSize;

  let whereClauses: any[] = [];
  
  const now = new Date().toISOString();
  
  if (status === 'active') {
    whereClauses.push({
      AND: [
        { isDisabled: { equals: false } },
        {
          OR: [
            { startsAt: { lte: now } },
            { startsAt: { equals: null } }
          ]
        },
        {
          OR: [
            { endsAt: { gte: now } },
            { endsAt: { equals: null } }
          ]
        }
      ]
    });
  } else if (status === 'disabled') {
    whereClauses.push({ isDisabled: { equals: true } });
  } else if (status === 'expired') {
    whereClauses.push({
      AND: [
        { endsAt: { not: { equals: null } } },
        { endsAt: { lt: now } }
      ]
    });
  }
  
  if (search) {
    whereClauses.push({
      OR: [
        { code: { contains: search, mode: 'insensitive' } },
        { discountRule: { description: { contains: search, mode: 'insensitive' } } },
      ],
    });
  }

  const where = whereClauses.length > 0 ? { AND: whereClauses } : {};
  
  const orderBy = sort ? { [sort.field]: sort.direction.toLowerCase() } : { createdAt: 'desc' };

  const query = `
    query GetFilteredDiscounts(
      $where: DiscountWhereInput
      $take: Int
      $skip: Int
      $orderBy: [DiscountOrderByInput!]
    ) {
      items: discounts(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        id
        code
        isDynamic
        isDisabled
        stackable
        startsAt
        endsAt
        metadata
        usageLimit
        usageCount
        validDuration
        createdAt
        updatedAt
        ordersCount
        regionsCount
        discountRule {
          id
          description
          type
          value
          allocation
        }
        orders(take: 5, orderBy: { createdAt: desc }) {
          id
          displayId
          status
          total
          discountTotal
          createdAt
          email
          currency {
            code
          }
        }
        regions(take: 5) {
          id
          name
          currencyCode
        }
      }
      count: discountsCount(where: $where)
    }
  `;

  const variables = { where, take, skip, orderBy: [orderBy] };
  
  return keystoneClient<ApiListResponse<Discount>>(query, variables);
}

export async function getDiscountStatusCounts(): Promise<KeystoneClientResponse<DiscountStatusCounts>> {
  const now = new Date().toISOString();
  
  const query = `
    query GetDiscountStatusCounts($now: DateTime!) {
      all: discountsCount
      disabled: discountsCount(where: { isDisabled: { equals: true } })
      active: discountsCount(where: {
        AND: [
          { isDisabled: { equals: false } },
          {
            OR: [
              { startsAt: { lte: $now } },
              { startsAt: { equals: null } }
            ]
          },
          {
            OR: [
              { endsAt: { gte: $now } },
              { endsAt: { equals: null } }
            ]
          }
        ]
      })
      expired: discountsCount(where: {
        AND: [
          { endsAt: { not: { equals: null } } },
          { endsAt: { lt: $now } }
        ]
      })
    }
  `;

  return keystoneClient<DiscountStatusCounts>(query, { now });
}

export async function getDiscount(id: string): Promise<KeystoneClientResponse<{ discount: Discount | null }>> {
  const query = `
    query GetDiscount($id: ID!) {
      discount(where: { id: $id }) {
        id
        code
        isDynamic
        isDisabled
        stackable
        startsAt
        endsAt
        metadata
        usageLimit
        usageCount
        validDuration
        createdAt
        updatedAt
        ordersCount
        regionsCount
        discountRule {
          id
          description
          type
          value
          allocation
        }
        orders(take: 20, orderBy: { createdAt: desc }) {
          id
          displayId
          status
          total
          discountTotal
          createdAt
          email
          currency {
            code
          }
        }
        regions {
          id
          name
          currencyCode
        }
      }
    }
  `;
  
  return keystoneClient<{ discount: Discount | null }>(query, { id });
}

export async function updateDiscount(
  id: string,
  input: DiscountUpdateInput
): Promise<MutationResponse<Discount>> {
  const query = `
    mutation UpdateDiscount($id: ID!, $data: DiscountUpdateInput!) {
      item: updateDiscount(where: { id: $id }, data: $data) {
        id
        code
        isDynamic
        isDisabled
        stackable
        startsAt
        endsAt
        metadata
        usageLimit
        usageCount
        validDuration
        createdAt
        updatedAt
        discountRule {
          id
          description
          type
          value
          allocation
        }
      }
    }
  `;
  try {
    const response = await keystoneClient<{ item: Discount | null }>(query, { id, data: input });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Discount not found or update failed." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during update.";
    console.error(`Error in updateDiscount for id ${id}:`, message);
    return { success: false, error: message };
  }
}

export async function deleteDiscount(id: string): Promise<MutationResponse<{ id: string }>> {
  const query = `
    mutation DeleteDiscount($id: ID!) {
      item: deleteDiscount(where: { id: $id }) {
        id
      }
    }
  `;
  try {
    const response = await keystoneClient<{ item: { id: string } | null }>(query, { id });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Discount not found or delete failed." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during delete.";
    console.error(`Error in deleteDiscount for id ${id}:`, message);
    return { success: false, error: message };
  }
}

export async function createDiscount(
  input: DiscountCreateInput
): Promise<MutationResponse<Discount>> {
  const query = `
    mutation CreateDiscount($data: DiscountCreateInput!) {
      item: createDiscount(data: $data) {
        id
        code
        isDynamic
        isDisabled
        stackable
        startsAt
        endsAt
        metadata
        usageLimit
        usageCount
        validDuration
        createdAt
        updatedAt
        discountRule {
          id
          description
          type
          value
          allocation
        }
      }
    }
  `;
   try {
    const response = await keystoneClient<{ item: Discount }>(query, { data: input });
    if (!response.success || !response.data?.item) {
      return { success: false, error: response.error || "Failed to create discount." };
    }
    return { success: true, data: response.data.item };
  } catch (error) {
    const message = error instanceof Error ? error.message : "An unexpected error occurred during creation.";
    console.error("Error in createDiscount:", message);
    return { success: false, error: message };
  }
}