"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, Tag, Percent, DollarSign, Truck, Package, Calendar, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import type { Discount, Order } from "../lib/types";

interface DiscountDetailsComponentProps {
  discount: Discount;
}

export function DiscountDetailsComponent({ discount }: DiscountDetailsComponentProps) {
  const [isOpen, setIsOpen] = useState(false);

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  const formatCurrency = (amount: number | null | undefined, currencyCode?: string | null) => {
    if (!amount) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode || "USD",
    }).format(amount / 100);
  };

  const getDiscountIcon = () => {
    switch (discount.discountRule?.type) {
      case "percentage":
        return <Percent className="h-5 w-5 text-primary" />;
      case "fixed":
        return <DollarSign className="h-5 w-5 text-primary" />;
      case "free_shipping":
        return <Truck className="h-5 w-5 text-primary" />;
      default:
        return <Tag className="h-5 w-5 text-primary" />;
    }
  };

  const getDiscountStatus = () => {
    const now = new Date();
    const startsAt = discount.startsAt ? new Date(discount.startsAt) : null;
    const endsAt = discount.endsAt ? new Date(discount.endsAt) : null;

    if (discount.isDisabled) return "disabled";
    if (endsAt && endsAt < now) return "expired";
    if (startsAt && startsAt > now) return "scheduled";
    return "active";
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "active":
        return "default";
      case "scheduled":
        return "secondary";
      case "disabled":
      case "expired":
        return "destructive";
      default:
        return "outline";
    }
  };

  const status = getDiscountStatus();
  const usagePercent = discount.usageLimit ? (discount.usageCount || 0) / discount.usageLimit * 100 : 0;

  const getDiscountValue = () => {
    if (!discount.discountRule) return "N/A";
    switch (discount.discountRule.type) {
      case "percentage":
        return `${discount.discountRule.value}%`;
      case "fixed":
        return formatCurrency(discount.discountRule.value);
      case "free_shipping":
        return "Free Shipping";
      default:
        return "N/A";
    }
  };

  return (
    <div className="px-4 py-4 hover:bg-muted/50 transition-colors">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              {getDiscountIcon()}
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium text-lg">{discount.code || "No Code"}</h3>
                <Badge variant={getStatusBadgeVariant(status)}>
                  {status}
                </Badge>
                {discount.stackable && (
                  <Badge variant="outline" className="text-xs">
                    Stackable
                  </Badge>
                )}
                {discount.isDynamic && (
                  <Badge variant="outline" className="text-xs">
                    Dynamic
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>{discount.discountRule?.description || "No description"}</span>
                <span className="font-medium">{getDiscountValue()}</span>
                {discount.discountRule?.allocation && (
                  <span className="text-xs">({discount.discountRule.allocation})</span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right mr-4">
              <div className="text-sm font-medium">
                {discount.usageCount || 0} / {discount.usageLimit || "∞"} uses
              </div>
              <div className="text-xs text-muted-foreground">
                {discount.ordersCount || 0} orders
              </div>
            </div>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-9 p-0">
                {isOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
                <span className="sr-only">Toggle details</span>
              </Button>
            </CollapsibleTrigger>
          </div>
        </div>
        <CollapsibleContent className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Usage & Validity Section */}
            <Card className="p-4">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Usage & Validity
              </h4>
              <div className="space-y-3">
                {discount.usageLimit && (
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Usage Progress</span>
                      <span className="font-medium">
                        {discount.usageCount || 0} / {discount.usageLimit}
                      </span>
                    </div>
                    <Progress value={usagePercent} className="h-2" />
                  </div>
                )}
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Start Date:</span>
                    <span>{formatDateTime(discount.startsAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">End Date:</span>
                    <span>{discount.endsAt ? formatDateTime(discount.endsAt) : "No expiry"}</span>
                  </div>
                  {discount.validDuration && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Valid Duration:</span>
                      <span>{discount.validDuration}</span>
                    </div>
                  )}
                </div>
              </div>
            </Card>

            {/* Recent Orders Section */}
            <Card className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Recent Orders
                </h4>
                <Badge variant="secondary">{discount.ordersCount || 0}</Badge>
              </div>
              <ScrollArea className="h-[150px]">
                {discount.orders && discount.orders.length > 0 ? (
                  <div className="space-y-2">
                    {discount.orders.map((order: Order) => (
                      <div
                        key={order.id}
                        className="p-2 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-sm">
                              #{order.displayId || order.id.slice(-8)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {order.email || "Guest"}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium">
                              Saved {formatCurrency(order.discountTotal, order.currency?.code)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatDate(order.createdAt)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-sm text-muted-foreground py-4">
                    No orders yet
                  </div>
                )}
              </ScrollArea>
            </Card>
          </div>

          {/* Regions */}
          {discount.regions && discount.regions.length > 0 && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center gap-2 text-sm">
                <span className="text-muted-foreground">Available in:</span>
                <div className="flex gap-1 flex-wrap">
                  {discount.regions.map((region) => (
                    <Badge key={region.id} variant="outline" className="text-xs">
                      {region.name} ({region.currencyCode})
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Additional Info */}
          <div className="mt-4 pt-4 border-t flex items-center justify-between text-xs text-muted-foreground">
            <div>
              Created: {formatDate(discount.createdAt)}
            </div>
            <div>
              Updated: {formatDate(discount.updatedAt)}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}