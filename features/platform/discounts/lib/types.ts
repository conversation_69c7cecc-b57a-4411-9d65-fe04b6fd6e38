export interface Discount {
  id: string;
  code?: string | null;
  isDynamic?: boolean | null;
  isDisabled?: boolean | null;
  stackable?: boolean | null;
  startsAt?: string | null;
  endsAt?: string | null;
  metadata?: any | null;
  usageLimit?: number | null;
  usageCount?: number | null;
  validDuration?: string | null;
  discountRule?: DiscountRule | null;
  orders?: Order[];
  ordersCount?: number;
  regions?: Region[];
  regionsCount?: number;
  createdAt?: string | null;
  updatedAt?: string | null;
}

export interface DiscountRule {
  id: string;
  description?: string | null;
  type?: 'fixed' | 'percentage' | 'free_shipping' | null;
  value?: number | null;
  allocation?: 'total' | 'item' | null;
  metadata?: any | null;
}

export interface Order {
  id: string;
  displayId?: string | null;
  status?: string | null;
  total?: number | null;
  discountTotal?: number | null;
  createdAt?: string | null;
  email?: string | null;
  currency?: {
    code: string;
  } | null;
}

export interface Region {
  id: string;
  name: string;
  currencyCode: string;
}

export interface DiscountStatusCounts {
  all: number;
  active: number;
  disabled: number;
  expired: number;
}

export interface DiscountUpdateInput {
  code?: string | null;
  isDynamic?: boolean | null;
  isDisabled?: boolean | null;
  stackable?: boolean | null;
  startsAt?: string | null;
  endsAt?: string | null;
  metadata?: any | null;
  usageLimit?: number | null;
  validDuration?: string | null;
  discountRule?: {
    connect?: { id: string };
    disconnect?: boolean;
  };
}

export interface DiscountCreateInput {
  code: string;
  isDynamic?: boolean | null;
  isDisabled?: boolean | null;
  stackable?: boolean | null;
  startsAt?: string | null;
  endsAt?: string | null;
  metadata?: any | null;
  usageLimit?: number | null;
  validDuration?: string | null;
  discountRule?: {
    create?: {
      description?: string | null;
      type: 'fixed' | 'percentage' | 'free_shipping';
      value: number;
      allocation?: 'total' | 'item' | null;
    };
    connect?: { id: string };
  };
}

// Sorting option interface
export interface SortOption {
  field: string;
  direction: 'ASC' | 'DESC';
}

// Response interfaces
export interface ApiListResponse<T> {
  items: T[];
  count: number;
}

export interface MutationResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}