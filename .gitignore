node_modules
.keystone
.keystone/admin
*.log
*.env
# making sure demo db is committed to repo so 
# one click deploy to vercel will work
!keystone.db

# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.repomix-output.txt
.repomixignore
repomix-output-new-storefront.xml
repomix-output-old-storefront.xml

**/.claude/settings.local.json
features/dashboard/components/repomix-output-tanstack query-nextjs-suspense-streaming.zip.xml

# Midscene.js dump files
midscene_run/dump
midscene_run/report
midscene_run/tmp
midscene_run/log
PLATFORM_PAGES_GENERATION_PROMPT.md
PLATFORM_PRODUCT_CATEGORY_PLAN.md
