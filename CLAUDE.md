# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Openfront is a comprehensive e-commerce platform consisting of:
1. Customer-facing storefront
2. Admin dashboard for store management
3. Backend API and database integration

The project uses Next.js (v15+) with App Router for the frontend, KeystoneJS (v6+) for the backend/API, and Prisma (v6+) as the ORM. It's built with TypeScript and includes payment integration with providers like Stripe and PayPal.

## Development Commands

### Essential Commands

```bash
# Start development server (builds Keystone, runs migrations, starts Next.js with Turbopack)
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint

# Generate database migrations
npm run migrate:gen

# Apply database migrations
npm run migrate
```

### Development Workflow

1. Start the development server with `npm run dev`
2. Make changes to your code
3. If you modify Keystone list schemas:
   - Regenerate schema with `keystone build --no-ui`
   - Run any necessary migrations with `npm run migrate:gen`

## Architecture

### Directory Structure

- `/app` - Next.js App Router pages and routes
  - `/(storefront)` - Customer-facing storefront (organized by country code)
  - `/dashboard` - Admin dashboard for store management
- `/keystone` - KeystoneJS configuration
  - `/models` - Data models (User, Product, Order, etc.)
  - `/mutations` - Custom GraphQL mutations
  - `/queries` - Custom GraphQL queries
- `/storefront` - Storefront UI components and logic
  - `/modules` - Feature-based components (products, cart, checkout, etc.)
  - `/lib` - Utility functions and data fetching
- `/components` - Reusable UI components
  - `/ui` - Base UI components using Radix UI primitives
- `/paymentProviderAdapters` - Integrations with payment services
- `/shippingProviderAdapters` - Integrations with shipping services

### Data Flow

1. **Frontend Requests**: The storefront and admin dashboard make GraphQL requests to fetch/modify data
2. **API Processing**: KeystoneJS GraphQL API handles requests, applying access control and validation
3. **Database Operations**: Prisma executes database operations based on GraphQL resolver logic
4. **Response Handling**: Data is returned to the frontend and rendered in the UI

### Authentication

- Uses KeystoneJS authentication system
- Admin access requires proper authentication and authorization
- Customer accounts can register, login, and manage their orders

## Working with the Codebase

### TypeScript Guidelines

- The project is working to improve type safety by reducing `any` usage
- When adding new code, use specific types rather than `any`
- Look for existing types in `/types` directory before creating new ones
- Use GraphQL introspection to determine correct types for API responses

### Frontend Development

- Follow existing patterns in similar components
- Use Tailwind CSS for styling following existing conventions
- Leverage the UI component library in `/components/ui`
- For new pages, follow the Next.js App Router conventions

### Backend Development

- New data models should be added to `/keystone/models`
- Custom mutations go in `/keystone/mutations`
- Follow existing access control patterns in similar models
- Generate and apply migrations after schema changes

### Testing

This repository doesn't appear to have a formal testing setup documented. When adding new code, follow existing patterns and manually test changes in development.

## Common Tasks

### Adding a New Product Feature

1. Add or modify the model in `/keystone/models`
2. Create UI components in `/storefront/modules` following existing patterns
3. Add any necessary GraphQL operations in `/keystone/mutations` or `/keystone/queries`
4. Implement the frontend UI in the appropriate `/app` route
5. Run migrations if schema changed with `npm run migrate:gen`

### Working with Payment Providers

1. Use the adapter pattern in `/paymentProviderAdapters`
2. Follow implementation patterns in existing adapters (stripe.ts, paypal.ts)
3. Connect to checkout flow in `/storefront/modules/checkout`

### Troubleshooting

- For API issues, check GraphQL responses and KeystoneJS logs
- For frontend issues, use browser developer tools to debug
- For database issues, verify Prisma schema matches expected structure