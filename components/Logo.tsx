import React from 'react';
import { cn } from '@/lib/utils/cn';
import { Space_Grotesk } from 'next/font/google';

const spaceGrotesk = Space_Grotesk({
  subsets: ['latin'],
  display: 'swap',
  adjustFontFallback: false,
});

interface LogoIconProps {
  className?: string;
  suffix?: string;
}

export const LogoIcon = ({ className, suffix = '' }: LogoIconProps) => {
  const id = (base: string) => `${base}${suffix}`;
  return (
    <svg
      width="200"
      height="200"
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className, 'text-sky-600 dark:text-blue-500')}
    >
      <g clipPath={`url(#${id('clip_')})`}>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M100 200C155.228 200 200 155.228 200 100C200 44.7715 155.228 0 100 0C44.7715 0 0 44.7715 0 100C0 155.228 44.7715 200 100 200ZM100 143.75C124.162 143.75 143.75 124.162 143.75 100C143.75 75.8375 124.162 56.25 100 56.25C75.8375 56.25 56.25 75.8375 56.25 100C56.25 124.162 75.8375 143.75 100 143.75Z"
          fill={`url(#${id('gradient_')})`}
        />
      </g>
      <defs>
        <linearGradient
          id={id('gradient_')}
          x1="100"
          y1="0"
          x2="100"
          y2="200"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="currentColor" />
          <stop offset="1" stopColor="currentColor" stopOpacity="0.6" />
        </linearGradient>
        <clipPath id={id('clip_')}>
          <rect width="200" height="200" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

interface LogoProps {
  className?: string;
}

export const Logo = ({ className }: LogoProps) => {
  return (
    <div className={cn(spaceGrotesk.className, className)}>
      <div className="flex items-center gap-2 text-zinc-700 dark:text-white">
        <LogoIcon className="size-4.5" suffix="-full" />
        <h1 className="mb-1 font-bold tracking-tight text-xl">
          openfront
        </h1>
      </div>
    </div>
  );
};
