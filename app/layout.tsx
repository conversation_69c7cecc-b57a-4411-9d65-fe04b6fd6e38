import { Toaster } from "@/components/ui/sonner";
import { Inter } from "next/font/google";

export const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} bg-background antialiased`}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
