
import { retrieveCart } from "@/features/storefront/lib/data/cart"
import { getUser } from "@/features/storefront/lib/data/user" // Assuming getUser is in user.ts, adjust if needed
// import { getBaseURL } from "@/features/storefront/lib/util/env" // Removed or replace if needed
import Footer from "@/features/storefront/modules/layout/templates/footer"
import Nav from "@/features/storefront/modules/layout/templates/nav"

// Assuming metadataBase is handled differently or not needed for now
// export const metadata: Metadata = {
//   metadataBase: new URL(getBaseURL()),
// }

export default async function PageLayout(props: { children: React.ReactNode }) {
  await getUser()
  await retrieveCart()
  // Removed shippingOptions fetching logic, adjust if FreeShippingPriceNudge needs it differently
  // let shippingOptions: any[] = []

  return (
    <>
      <Nav />
      {/* TODO: [Refactor Alignment] FreeShippingPriceNudge component usage commented out as it's not present in the old storefront and caused build/runtime issues. Re-evaluate integration later. */}
      {/* {cart && (
        <FreeShippingPriceNudge
          variant="popup"
          cart={cart}
          shippingOptions={[]} 
        />
      )} */}
      {props.children}
      <Footer />
    </>
  )
}
