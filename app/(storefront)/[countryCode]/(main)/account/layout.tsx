import { getUser } from "@/features/storefront/lib/data/user";
import AccountLayout from "@/features/storefront/modules/account/templates/account-layout";
import type { ReactNode } from "react";

export default async function AccountPageLayout({
  dashboard,
  login,
}: {
  dashboard?: ReactNode
  login?: ReactNode
}) {
  const user = await getUser()

  return (
    <AccountLayout customer={user}>
      {user ? dashboard : login}
    </AccountLayout>
  )
}
