import { Metadata } from "next"

import Overview from "@/features/storefront/modules/account/components/overview" 
import { notFound } from "next/navigation"
import { getUserWithOrders } from "@/features/storefront/lib/data/user";

export const metadata: Metadata = {
  title: "Account",
  description: "Overview of your account activity.",
}

export default async function OverviewTemplate() {
  const user = await getUserWithOrders();

  if (!user?.id) notFound();

  return <Overview user={user} orders={user.orders} />;
}
