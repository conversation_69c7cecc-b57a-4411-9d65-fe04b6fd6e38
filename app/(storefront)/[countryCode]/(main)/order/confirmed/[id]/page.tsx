import { retrieveOrder } from "@/features/storefront/lib/data/orders" 
import OrderCompletedTemplate from "@/features/storefront/modules/order/templates/order-completed-template" 
import { Metadata } from "next"
import { notFound } from "next/navigation"
import type { Order } from "@/features/storefront/types/graphql-types"
 
// Add searchParams to Props
type Props = {
  params: Promise<{ id: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export const metadata: Metadata = {
  title: "Order Confirmed",
  description: "You purchase was successful",
}

// Update function signature to accept searchParams
export default async function OrderConfirmedPage({ params: paramsPromise, searchParams: searchParamsPromise }: Props) {
  const params = await paramsPromise
  const searchParams = await searchParamsPromise

  // Extract secretKey (assuming it's passed as 'key')
  const secretKey = typeof searchParams?.key === 'string' ? searchParams.key : null

  // Call retrieveOrder with id and secretKey
  const order: Order | null = await retrieveOrder(params.id, secretKey).catch(() => null)

  if (!order) {
    return notFound()
  }

  return <OrderCompletedTemplate order={order} />
}
