import CartTemplate from "@/features/storefront/modules/cart/templates"
import { retrieveCart } from "@/features/storefront/lib/data/cart"
import { getUser } from "@/features/storefront/lib/data/user"

export const metadata = {
  title: "Cart",
  description: "View your cart",
}

export default async function Cart() {
  const cart = await retrieveCart()
  const user = await getUser()

  return <CartTemplate cart={cart} user={user} />
}
