import { notFound } from "next/navigation";

import Wrapper from "@/features/storefront/modules/checkout/components/payment-wrapper";
import CheckoutForm from "@/features/storefront/modules/checkout/templates/checkout-form";
import CheckoutSummary from "@/features/storefront/modules/checkout/templates/checkout-summary";
import { retrieveCart } from "@/features/storefront/lib/data/cart";
import { getUser } from "@/features/storefront/lib/data/user";

export const metadata = {
  title: "Checkout",
};

const fetchCart = async () => {
  const cart = await retrieveCart();

  if (!cart) {
    return notFound();
  }

  return cart;
};

export default async function Checkout() {
  const cart = await fetchCart();
  const customer = await getUser();

  return (
    <div className="grid grid-cols-1 sm:grid-cols-[1fr_416px] max-w-[1440px] w-full mx-auto px-6 gap-x-40 py-12">
      <Wrapper cart={cart}>
        <CheckoutForm cart={cart} customer={customer} />
      </Wrapper>
      <CheckoutSummary cart={cart} />
    </div>
  );
}