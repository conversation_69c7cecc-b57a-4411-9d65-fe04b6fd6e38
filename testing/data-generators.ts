// Test data generators for Dasher6
// You can expand these functions to generate more complex test data

export const createUser = (overrides = {}) => ({
  id: `user-${Math.floor(Math.random() * 10000)}`,
  name: `Test User ${Math.floor(Math.random() * 10000)}`,
  email: `test${Math.floor(Math.random() * 10000)}@example.com`,
  password: 'password123',
  ...overrides,
});

export const createProduct = (overrides = {}) => ({
  id: `product-${Math.floor(Math.random() * 10000)}`,
  name: `Test Product ${Math.floor(Math.random() * 10000)}`,
  description: 'This is a test product description',
  price: Math.floor(Math.random() * 1000) / 10,
  ...overrides,
});

export const createOrder = (overrides = {}) => ({
  id: `order-${Math.floor(Math.random() * 10000)}`,
  total: Math.floor(Math.random() * 10000) / 100,
  status: 'pending',
  ...overrides,
});

export const createAddress = (overrides = {}) => ({
  id: `address-${Math.floor(Math.random() * 10000)}`,
  line1: '123 Test St',
  line2: '',
  city: 'Test City',
  state: 'Test State',
  postalCode: '12345',
  country: 'Test Country',
  ...overrides,
});