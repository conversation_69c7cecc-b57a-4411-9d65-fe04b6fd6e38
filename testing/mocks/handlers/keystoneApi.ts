import { graphql, HttpResponse } from 'msw';

// Mock handlers for common Keystone GraphQL operations
export const keystoneHandlers = [
  // User queries
  graphql.query('GetUser', () => {
    return HttpResponse.json({
      data: {
        user: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
        }
      }
    });
  }),
  
  // Product queries
  graphql.query('GetProducts', () => {
    return HttpResponse.json({
      data: {
        products: [
          {
            id: '1',
            name: 'Test Product',
            price: 100,
            description: 'Test description',
          }
        ]
      }
    });
  }),
  
  // Order queries
  graphql.query('GetOrders', () => {
    return HttpResponse.json({
      data: {
        orders: [
          {
            id: '1',
            total: 100,
            status: 'complete',
          }
        ]
      }
    });
  }),
  
  // Mutations
  graphql.mutation('CreateOrder', () => {
    return HttpResponse.json({
      data: {
        createOrder: {
          id: '2',
          total: 200,
          status: 'pending',
        }
      }
    });
  }),
];