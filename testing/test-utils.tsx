import {
  render as rtlRender,
  waitForElementToBeRemoved,
  screen,
} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReactNode } from 'react';

// Import your app providers here
// For example: ThemeProvider, SWRConfig, etc.

export const waitForLoadingToFinish = () =>
  waitForElementToBeRemoved(
    () => [
      ...screen.queryAllByTestId(/loading/i),
      ...screen.queryAllByText(/loading/i),
    ],
    { timeout: 4000 },
  );

// Create wrapper with all providers needed for tests
const AllProviders = ({ children }: { children: ReactNode }) => {
  return (
    // Add your providers here, for example:
    // <ThemeProvider>
    //   <SWRConfig value={{ dedupingInterval: 0, provider: () => new Map() }}>
    //     {children}
    //   </SWRConfig>
    // </ThemeProvider>
    <>{children}</>
  );
};

export const renderApp = (ui: any, options = {}) => {
  const returnValue = {
    ...rtlRender(ui, {
      wrapper: AllProviders,
      ...options,
    }),
  };

  return returnValue;
};

export * from '@testing-library/react';
export { userEvent, rtlRender };